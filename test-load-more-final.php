<?php
/**
 * Final Load More Test
 * This will test the complete load more functionality
 */

// Load WordPress
require_once('../../../wp-load.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Final Load More Test - Ag-Coupon</title>
    
    <!-- Load theme styles and scripts -->
    <?php wp_head(); ?>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        .coupon-grid-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .load-more-test-area {
            border: 2px dashed #007cba;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Final Load More Test</h1>
        <p>This page tests the complete load more functionality with the enhanced coupon cards.</p>
        
        <div id="test-status"></div>
        
        <button onclick="runCompleteTest()">🧪 Run Complete Test</button>
        <button onclick="testLoadMoreClick()">🖱️ Test Load More Click</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div class="test-container">
        <h2>📋 Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>🎯 Live Load More Test</h2>
        <p>This section simulates the actual home page with enhanced coupon cards:</p>
        
        <div class="load-more-test-area">
            <!-- Coupon Grid Container -->
            <div class="latest-coupons-grid-container mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="latest-coupons-grid">
                    <?php
                    // Get initial coupons (first page)
                    $initial_coupons = get_posts(array(
                        'post_type' => 'coupon',
                        'posts_per_page' => 4,
                        'post_status' => 'publish',
                        'orderby' => 'date',
                        'order' => 'DESC'
                    ));
                    
                    foreach ($initial_coupons as $coupon) {
                        echo '<div class="coupon-grid-item">';
                        echo '<h3>' . get_the_title($coupon->ID) . '</h3>';
                        echo '<p><strong>ID:</strong> ' . $coupon->ID . '</p>';
                        echo '<p><strong>Type:</strong> ' . get_post_meta($coupon->ID, '_wpc_coupon_type', true) . '</p>';
                        echo '<p><strong>Code:</strong> ' . get_post_meta($coupon->ID, '_wpc_coupon_code', true) . '</p>';
                        echo '</div>';
                    }
                    ?>
                </div>

                <!-- Load More Button - Exact copy from our enhanced system -->
                <div class="load-more-container mt-12 mb-8">
                    <div class="flex justify-center">
                        <div class="load-more">
                            <button type="button"
                                   class="load-more-btn inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                   data-doing="load_coupons"
                                   data-next-page="2"
                                   data-loading-text="جاري التحميل..."
                                   data-per-page="4"
                                   data-args='{"layout":"","posts_per_page":"","num_words":"","hide_expired":""}'>
                                
                                <!-- Button Text -->
                                <span class="btn-text">تحميل المزيد من الكوبونات</span>
                                
                                <!-- Loading Spinner -->
                                <span class="loading-spinner hidden ml-2">
                                    <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                
                                <!-- Button Icon -->
                                <span class="btn-icon ml-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php wp_footer(); ?>

    <script>
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            // Auto-scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            addResult('Test results cleared', 'info');
        }
        
        function runCompleteTest() {
            addResult('=== STARTING COMPLETE LOAD MORE TEST ===', 'info');
            
            // Test 1: Check JavaScript objects
            addResult('Testing JavaScript environment...', 'info');
            
            if (typeof $ === 'undefined') {
                addResult('❌ jQuery not loaded', 'error');
                return;
            } else {
                addResult('✅ jQuery loaded', 'success');
            }
            
            if (typeof ST === 'undefined') {
                addResult('❌ ST object not found', 'error');
                return;
            } else {
                addResult('✅ ST object found', 'success');
                addResult(`AJAX URL: ${ST.ajax_url}`, 'info');
                addResult(`Nonce: ${ST._wpnonce}`, 'info');
            }
            
            if (typeof window.AgCoupon === 'undefined') {
                addResult('❌ AgCoupon object not found', 'error');
            } else {
                addResult('✅ AgCoupon object found', 'success');
                addResult(`AgCoupon initialized: ${window.AgCoupon.initialized}`, 'info');
            }
            
            // Test 2: Check load more button
            addResult('Testing load more button...', 'info');
            const $button = $('.load-more-btn');
            if ($button.length === 0) {
                addResult('❌ Load more button not found', 'error');
                return;
            } else {
                addResult(`✅ Load more button found (${$button.length})`, 'success');
                addResult(`Button text: "${$button.find('.btn-text').text().trim()}"`, 'info');
                addResult(`Button data: ${JSON.stringify($button.data())}`, 'info');
            }
            
            // Test 3: Check target container
            addResult('Testing target container...', 'info');
            const $container = $('#latest-coupons-grid');
            if ($container.length === 0) {
                addResult('❌ Target container not found', 'error');
                return;
            } else {
                addResult('✅ Target container found', 'success');
                addResult(`Container children: ${$container.children().length}`, 'info');
            }
            
            // Test 4: Test AJAX directly
            addResult('Testing AJAX directly...', 'info');
            $.ajax({
                url: ST.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpcoupon_coupon_ajax',
                    st_doing: 'load_coupons',
                    next_page: 2,
                    _wpnonce: ST._wpnonce,
                    args: {
                        layout: '',
                        posts_per_page: '4',
                        num_words: '',
                        hide_expired: ''
                    }
                },
                success: function(response) {
                    addResult('✅ AJAX call successful', 'success');
                    addResult(`Response success: ${response.success}`, 'info');
                    if (response.data && response.data.content) {
                        addResult(`Content length: ${response.data.content.length} characters`, 'success');
                        addResult(`Next page: ${response.data.next_page}`, 'info');
                        addResult(`Max pages: ${response.data.max_pages}`, 'info');
                    } else {
                        addResult('❌ No content in response', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    addResult(`❌ AJAX call failed: ${error}`, 'error');
                    addResult(`Status: ${status}`, 'error');
                    addResult(`Response: ${xhr.responseText}`, 'error');
                }
            });
            
            addResult('=== COMPLETE TEST FINISHED ===', 'info');
        }
        
        function testLoadMoreClick() {
            addResult('Testing load more button click...', 'info');
            
            const $button = $('.load-more-btn');
            if ($button.length === 0) {
                addResult('❌ No load more button found', 'error');
                return;
            }
            
            addResult('Clicking load more button...', 'info');
            
            // Monitor for new content
            const initialCount = $('#latest-coupons-grid').children().length;
            addResult(`Initial coupon count: ${initialCount}`, 'info');
            
            // Set up a listener for new content
            $(document).one('ag-coupon:content-loaded', function(event, newContent) {
                addResult(`✅ New content loaded! Added ${newContent.length} items`, 'success');
                const finalCount = $('#latest-coupons-grid').children().length;
                addResult(`Final coupon count: ${finalCount}`, 'success');
            });
            
            // Click the button
            $button.trigger('click');
            
            // Check after a delay
            setTimeout(function() {
                const finalCount = $('#latest-coupons-grid').children().length;
                if (finalCount > initialCount) {
                    addResult(`✅ Load more successful! Count increased from ${initialCount} to ${finalCount}`, 'success');
                } else {
                    addResult(`❌ Load more failed. Count remained at ${finalCount}`, 'error');
                }
            }, 3000);
        }
        
        // Auto-run test when page loads
        $(document).ready(function() {
            setTimeout(function() {
                addResult('Page loaded, running automatic test...', 'info');
                runCompleteTest();
            }, 2000);
        });
    </script>
</body>
</html>
