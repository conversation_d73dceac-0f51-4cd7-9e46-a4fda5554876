<?php
/**
 * Test AJAX HTML Structure
 * Quick test to verify the AJAX output structure is correct
 */

// Load WordPress
require_once('wp-config.php');

echo "<h1>AJAX HTML Structure Test</h1>\n";

// Test 1: Check if functions exist
echo "<h2>1. Function Check</h2>\n";
echo "wpcoupon_ajax_coupons: " . (function_exists('wpcoupon_ajax_coupons') ? '✅' : '❌') . "<br>\n";
echo "render_enhanced_coupon_card: " . (function_exists('render_enhanced_coupon_card') ? '✅' : '❌') . "<br>\n";
echo "wpcoupon_render_default_coupon_card: " . (function_exists('wpcoupon_render_default_coupon_card') ? '✅' : '❌') . "<br>\n";

// Test 2: Simulate AJAX request
echo "<h2>2. AJAX Structure Test</h2>\n";

// Set up AJAX context
$_REQUEST['st_doing'] = 'load_coupons';
$_REQUEST['next_page'] = 2;
$_REQUEST['args'] = array(
    'layout' => '',
    'posts_per_page' => '3',
    'num_words' => '',
    'hide_expired' => ''
);

// Define AJAX context
define('DOING_AJAX', true);

if (function_exists('wpcoupon_ajax_coupons')) {
    try {
        $result = wpcoupon_ajax_coupons('load_coupons');
        
        echo "<h3>AJAX Response Analysis:</h3>\n";
        echo "<strong>Content Length:</strong> " . strlen($result['content']) . " characters<br>\n";
        echo "<strong>Next Page:</strong> " . $result['next_page'] . "<br>\n";
        echo "<strong>Max Pages:</strong> " . $result['max_pages'] . "<br>\n";
        
        if (!empty($result['content'])) {
            // Analyze HTML structure
            $content = $result['content'];
            
            // Count different wrapper types
            $grid_items = substr_count($content, 'class="coupon-grid-item"');
            $direct_articles = preg_match_all('/<article[^>]*class="[^"]*ag-coupon-card[^"]*"/', $content);
            $style_divs = substr_count($content, '<div style>');
            
            echo "<h3>HTML Structure Analysis:</h3>\n";
            echo "<strong>✅ Grid Items (coupon-grid-item):</strong> $grid_items<br>\n";
            echo "<strong>📄 Direct Articles:</strong> $direct_articles<br>\n";
            echo "<strong>⚠️ Style Divs:</strong> $style_divs<br>\n";
            
            // Check for debug comments
            $ajax_comments = substr_count($content, '<!-- AJAX');
            echo "<strong>🔍 Debug Comments:</strong> $ajax_comments<br>\n";
            
            // Show structure preview
            echo "<h3>Structure Preview (First 1000 chars):</h3>\n";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow: auto;'>";
            echo htmlspecialchars(substr($content, 0, 1000));
            echo "</pre>\n";
            
            // Check if structure is consistent
            if ($grid_items > 0 && $direct_articles == 0 && $style_divs == 0) {
                echo "<div style='color: green; font-weight: bold; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50; margin: 10px 0;'>";
                echo "✅ SUCCESS: HTML structure is correct! All coupons are properly wrapped in grid items.";
                echo "</div>\n";
            } else {
                echo "<div style='color: red; font-weight: bold; padding: 10px; background: #ffeaea; border: 1px solid #f44336; margin: 10px 0;'>";
                echo "❌ ISSUE: Mixed HTML structure detected. Some coupons may not be properly wrapped.";
                echo "</div>\n";
            }
            
        } else {
            echo "<p style='color: red;'>❌ No content returned from AJAX!</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ wpcoupon_ajax_coupons function not found!</p>\n";
}

echo "<h2>3. Template Override Test</h2>\n";

// Test the template override function
if (function_exists('wpcoupon_override_coupon_template_part')) {
    echo "✅ Template override function exists<br>\n";
    
    // Test if it triggers for AJAX requests
    $test_result = wpcoupon_override_coupon_template_part('test-template', 'loop/loop-coupon', 'cat');
    
    if (empty($test_result)) {
        echo "✅ Template override is active for AJAX requests<br>\n";
    } else {
        echo "⚠️ Template override returned: " . $test_result . "<br>\n";
    }
} else {
    echo "❌ Template override function not found<br>\n";
}

echo "<hr><p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
