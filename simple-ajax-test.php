<?php
/**
 * Simple AJAX Test
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "<h1>Simple AJAX Test</h1>\n";

// Test 1: Check if AJAX action is registered
echo "<h2>1. AJAX Action Check</h2>\n";
echo "wpcoupon_coupon_ajax action registered: ";

global $wp_filter;
if (isset($wp_filter['wp_ajax_wpcoupon_coupon_ajax']) || isset($wp_filter['wp_ajax_nopriv_wpcoupon_coupon_ajax'])) {
    echo "✅ YES<br>\n";
} else {
    echo "❌ NO<br>\n";
}

// Test 2: Check if function exists
echo "<h2>2. Function Check</h2>\n";
echo "wpcoupon_ajax_coupons function exists: " . (function_exists('wpcoupon_ajax_coupons') ? '✅ YES' : '❌ NO') . "<br>\n";

// Test 3: Direct function call
echo "<h2>3. Direct Function Call</h2>\n";
if (function_exists('wpcoupon_ajax_coupons')) {
    // Set up request data
    $_REQUEST['st_doing'] = 'load_coupons';
    $_REQUEST['next_page'] = 2;
    $_REQUEST['args'] = array(
        'layout' => '',
        'posts_per_page' => '3',
        'num_words' => '',
        'hide_expired' => ''
    );
    
    try {
        $result = wpcoupon_ajax_coupons('load_coupons');
        echo "✅ Function call successful<br>\n";
        echo "Content length: " . strlen($result['content']) . " characters<br>\n";
        echo "Next page: " . $result['next_page'] . "<br>\n";
        echo "Max pages: " . $result['max_pages'] . "<br>\n";
        
        if (!empty($result['content'])) {
            echo "<h3>Content Preview:</h3>\n";
            echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 200px; overflow: auto;'>";
            echo htmlspecialchars(substr($result['content'], 0, 500)) . "...";
            echo "</div>\n";
        }
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "❌ Function not available<br>\n";
}

// Test 4: Check nonce
echo "<h2>4. Nonce Check</h2>\n";
$nonce = wp_create_nonce('wpcoupon_ajax_nonce');
echo "Generated nonce: " . $nonce . "<br>\n";
echo "Nonce verification: " . (wp_verify_nonce($nonce, 'wpcoupon_ajax_nonce') ? '✅ VALID' : '❌ INVALID') . "<br>\n";

// Test 5: Simulate AJAX call
echo "<h2>5. Simulate AJAX Call</h2>\n";
echo "<button onclick='testAjax()'>Test AJAX</button><br>\n";
echo "<div id='ajax-result'></div>\n";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testAjax() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = 'Testing AJAX...';
    
    $.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'wpcoupon_coupon_ajax',
            st_doing: 'load_coupons',
            next_page: 2,
            _wpnonce: '<?php echo $nonce; ?>',
            args: {
                layout: '',
                posts_per_page: '3',
                num_words: '',
                hide_expired: ''
            }
        },
        success: function(response) {
            resultDiv.innerHTML = '<h3>AJAX Success:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>';
        },
        error: function(xhr, status, error) {
            resultDiv.innerHTML = '<h3>AJAX Error:</h3><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>';
        }
    });
}
</script>
