<?php
/**
 * Test Home Page Load More Button
 */

// Load WordPress
require_once('../../../wp-load.php');

// Get latest coupons like the home page does
$latest_coupons = get_posts(array(
    'post_type' => 'coupon',
    'posts_per_page' => 8,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC'
));

echo "<h1>Home Page Load More Test</h1>\n";
echo "<p>Found " . count($latest_coupons) . " latest coupons</p>\n";

// Test the render_coupons_grid function
if (function_exists('render_coupons_grid')) {
    echo "<h2>Testing render_coupons_grid function</h2>\n";
    echo "<div style='border: 2px solid red; padding: 10px;'>\n";
    
    render_coupons_grid($latest_coupons, 'default', array(
        'columns' => 4,
        'columns_tablet' => 2,
        'columns_mobile' => 1,
        'gap' => 6,
        'container_class' => 'latest-coupons-grid-container mb-8',
        'section' => 'home-latest-coupons-grid',
        'track_coupons' => true,
        'show_load_more' => true,
        'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon')
    ));
    
    echo "</div>\n";
} else {
    echo "<p style='color: red;'>render_coupons_grid function not found!</p>\n";
}

// Add some JavaScript to test the button
?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    console.log('=== HOME PAGE LOAD MORE TEST ===');
    
    // Check for load more buttons
    const $buttons = $('.load-more-btn, .ag-load-more-coupons');
    console.log('Load more buttons found:', $buttons.length);
    
    $buttons.each(function(i) {
        const $btn = $(this);
        console.log(`Button ${i+1}:`, {
            element: this,
            classes: $btn.attr('class'),
            data: $btn.data(),
            text: $btn.text().trim()
        });
    });
    
    // Check for target containers
    const $container = $('#latest-coupons-grid');
    console.log('Target container (#latest-coupons-grid):', {
        found: $container.length > 0,
        element: $container[0],
        children: $container.children().length
    });
    
    // Test button click
    $(document).on('click', '.load-more-btn', function(e) {
        e.preventDefault();
        console.log('Load more button clicked!');
        alert('Load more button clicked! Check console for details.');
    });
});
</script>

<style>
    .load-more-btn {
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }
    .load-more-btn:hover {
        background: #005a87;
    }
</style>
