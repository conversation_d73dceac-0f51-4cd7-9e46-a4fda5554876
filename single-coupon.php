<?php

/**
 * Single Coupon Template - Tailwind CSS Design
 *
 * @package Ag-Coupon
 */
get_header();

/**
 * Hooks wpcoupon_after_header
 *
 * @see wpcoupon_page_header();
 *
 */
do_action('wpcoupon_after_header');
$layout = wpcoupon_get_site_layout();

global $post;
wpcoupon_setup_coupon($post);
the_post();

// Setup coupon and store objects using proper template functions
$coupon_obj = wpcoupon_coupon();
$store_obj = wpcoupon_store();

$has_thumb = wpcoupon_maybe_show_coupon_thumb();
$has_expired = $coupon_obj->has_expired();
$coupon_type = $coupon_obj->get_type();
$is_featured = get_post_meta($coupon_obj->ID, '_wpc_exclusive', true);
$discount_value = get_post_meta($coupon_obj->ID, '_wpc_coupon_save', true);
$is_free_shipping = get_post_meta($coupon_obj->ID, '_wpc_free_shipping', true);
$coupon_code = $coupon_obj->get_code();
$coupon_expires = $coupon_obj->get_expires();
$total_used = $coupon_obj->get_total_used();
$coupon_type_text = $coupon_obj->get_coupon_type_text();
?>

<!-- Tailwind CSS Single Coupon Page -->
<div id="content-wrap" class="single-coupon-container container <?php echo esc_attr($layout); ?> py-8">

	<div id="primary" class="coupon-single content-area">

		<main id="main" class="site-main" role="main">

			<!-- Single Coupon Card -->
			<article data-id="<?php echo $coupon_obj->ID; ?>"
				class="single-coupon-card bg-white rounded-2xl shadow-coupon-hover overflow-hidden <?php echo $has_thumb ? 'has-thumb' : 'no-thumb'; ?> c-type-<?php echo esc_attr($coupon_type); ?> <?php echo ($has_expired) ? 'coupon-expired opacity-75' : 'coupon-live'; ?> <?php echo $is_featured ? 'ring-2 ring-orange-400' : ''; ?> mb-8">

				<!-- Status Badges -->
				<?php if ( $has_expired ) { ?>
					<div class="absolute top-4 right-4 z-10">
						<span class="coupon-badge-expired text-sm px-3 py-1">
							<?php esc_html_e( 'منتهي الصلاحية', 'wp-coupon' ); ?>
						</span>
					</div>
				<?php } ?>

				<?php if ( $is_featured ) { ?>
					<div class="absolute top-4 left-4 z-10">
						<span class="coupon-badge-featured text-sm px-3 py-1">
							<?php esc_html_e( 'حصري', 'wp-coupon' ); ?>
						</span>
					</div>
				<?php } ?>

				<!-- Coupon Header -->
				<div class="coupon-header bg-gradient-to-r from-gray-50 to-gray-100 p-6">
					<div class="flex items-center space-x-6 space-x-reverse">

						<!-- Store Thumbnail -->
						<?php if ($has_thumb) { ?>
						<div class="store-thumb-container flex-shrink-0">
							<div class="store-logo w-24 h-24 rounded-xl overflow-hidden bg-white shadow-md">
								<?php if (!is_tax('coupon_store')) { ?>
									<a href="<?php echo esc_url($coupon_obj->get_store_url()); ?>" class="block hover:opacity-80 transition-opacity duration-200">
										<?php echo $coupon_obj->get_thumb('wpcoupon_medium-thumb'); ?>
									</a>
								<?php } else { ?>
									<?php echo $coupon_obj->get_thumb('wpcoupon_medium-thumb'); ?>
								<?php } ?>
							</div>
						</div>
						<?php } ?>

						<!-- Coupon Info -->
						<div class="coupon-info flex-1">
							<!-- Coupon Type Badge -->
							<div class="mb-3">
								<?php
								switch ( $coupon_type ) {
									case 'code':
										echo '<span class="coupon-badge-code text-sm">' . esc_html__( 'كود خصم', 'wp-coupon' ) . '</span>';
										break;
									case 'sale':
										echo '<span class="coupon-badge-sale text-sm">' . esc_html__( 'عرض', 'wp-coupon' ) . '</span>';
										break;
									case 'print':
										echo '<span class="coupon-badge-print text-sm">' . esc_html__( 'قابل للطباعة', 'wp-coupon' ) . '</span>';
										break;
								}
								?>
							</div>

							<!-- Coupon Title -->
							<h1 class="coupon-title text-3xl font-bold text-gray-900 mb-4 leading-tight">
								<?php echo get_the_title($coupon_obj->ID); ?>
							</h1>

							<!-- Store Link -->
							<?php if (!is_tax('coupon_store') && $store_obj) { ?>
							<div class="store-link">
								<a href="<?php echo esc_url($coupon_obj->get_store_url()); ?>"
								   class="inline-flex items-center text-secondary-600 hover:text-secondary-700 font-medium transition-colors duration-200">
									<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 18a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
									</svg>
									<?php printf( esc_html__( 'زيارة متجر %s', 'wp-coupon' ), esc_html($store_obj->name) ); ?>
									<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
									</svg>
								</a>
							</div>
							<?php } ?>
						</div>
					</div>
				</div>



					<!-- Coupon Action Section -->
					<div class="coupon-action-section bg-white p-6 border-t border-gray-100">
						<?php
						switch ($coupon_type) {

							case 'sale':
						?>
						<!-- Sale/Deal Button -->
						<div class="text-center">
							<a href="<?php echo esc_url($coupon_obj->get_destination_url()); ?>" target="_blank"
							   class="btn-secondary inline-flex items-center justify-center w-full max-w-md py-4 px-6 text-lg font-bold rounded-xl transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary-300">
								<?php esc_html_e('احصل على العرض الآن', 'wp-coupon'); ?>
								<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
									<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
								</svg>
							</a>
						</div>
						<?php
								break;
							case 'print':
							?>
						<!-- Print Coupon Button -->
						<div class="text-center">
							<a href="<?php echo esc_url($coupon_obj->get_destination_url()); ?>" target="_blank"
							   class="bg-green-500 hover:bg-green-600 text-white inline-flex items-center justify-center w-full max-w-md py-4 px-6 text-lg font-bold rounded-xl transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-300">
								<?php esc_html_e('اطبع الكوبون', 'wp-coupon'); ?>
								<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path>
								</svg>
							</a>
						</div>
						<?php
								break;
							default:
							?>
						<!-- Code Coupon Section -->
						<div class="coupon-code-section">
							<!-- Code Display and Copy -->
							<div class="bg-gray-50 rounded-xl p-6 mb-6">
								<div class="text-center mb-4">
									<h3 class="text-lg font-semibold text-gray-900 mb-2"><?php esc_html_e('كود الخصم', 'wp-coupon'); ?></h3>
									<p class="text-sm text-gray-600"><?php esc_html_e('انسخ الكود واستخدمه عند الدفع', 'wp-coupon'); ?></p>
								</div>

								<div class="flex items-center bg-white rounded-lg border-2 border-dashed border-primary-300 p-4 mb-4">
									<input id="code-text" type="text"
										   class="flex-1 text-center text-2xl font-mono font-bold text-primary-700 bg-transparent border-none outline-none"
										   autocomplete="off" readonly
										   value="<?php echo esc_attr($coupon_code); ?>">
									<button onclick="copyToClipBoard()"
											class="btn-primary ml-4 py-2 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-400">
										<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
											<path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
											<path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
										</svg>
										<span class="copy-btn"><?php esc_html_e('نسخ', 'wp-coupon'); ?></span>
									</button>
								</div>
							</div>

							<!-- Go to Store Button -->
							<div class="text-center">
								<a href="<?php echo esc_url($coupon_obj->get_destination_url()); ?>"
								   onclick="copyToClipBoard()" target="_blank"
								   class="btn-secondary inline-flex items-center justify-center w-full max-w-md py-4 px-6 text-lg font-bold rounded-xl transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary-300">
									<?php esc_html_e('اذهب إلى المتجر', 'wp-coupon'); ?>
									<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
									</svg>
								</a>
							</div>
						</div>

						<?php
						}
						?>

						<!-- Coupon Statistics -->
						<div class="coupon-stats bg-gray-50 p-6 border-t border-gray-100">
							<div class="grid grid-cols-2 md:grid-cols-4 gap-4">

								<!-- Discount Value -->
								<?php if ( $discount_value ) { ?>
								<div class="stat-item text-center bg-white rounded-lg p-4 shadow-sm">
									<div class="stat-icon text-primary-500 mb-2">
										<svg class="w-6 h-6 mx-auto" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
										</svg>
									</div>
									<div class="stat-value text-lg font-bold text-gray-900"><?php echo esc_html( $discount_value ); ?></div>
									<div class="stat-label text-xs text-gray-600"><?php esc_html_e( 'قيمة الخصم', 'wp-coupon' ); ?></div>
								</div>
								<?php } ?>

								<!-- Total Used -->
								<div class="stat-item text-center bg-white rounded-lg p-4 shadow-sm">
									<div class="stat-icon text-secondary-500 mb-2">
										<svg class="w-6 h-6 mx-auto" fill="currentColor" viewBox="0 0 20 20">
											<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
										</svg>
									</div>
									<div class="stat-value text-lg font-bold text-gray-900"><?php echo esc_html($total_used); ?></div>
									<div class="stat-label text-xs text-gray-600"><?php esc_html_e( 'مستخدم', 'wp-coupon' ); ?></div>
								</div>

								<!-- Coupon Type -->
								<div class="stat-item text-center bg-white rounded-lg p-4 shadow-sm">
									<div class="stat-icon text-green-500 mb-2">
										<svg class="w-6 h-6 mx-auto" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z" clip-rule="evenodd"></path>
										</svg>
									</div>
									<div class="stat-value text-sm font-semibold text-gray-900"><?php echo esc_html($coupon_type_text); ?></div>
									<div class="stat-label text-xs text-gray-600"><?php esc_html_e( 'نوع الكوبون', 'wp-coupon' ); ?></div>
								</div>

								<!-- Expiry Date -->
								<div class="stat-item text-center bg-white rounded-lg p-4 shadow-sm">
									<div class="stat-icon <?php echo $has_expired ? 'text-red-500' : 'text-blue-500'; ?> mb-2">
										<svg class="w-6 h-6 mx-auto" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
										</svg>
									</div>
									<div class="stat-value text-sm font-semibold <?php echo $has_expired ? 'text-red-600' : 'text-gray-900'; ?>">
										<?php echo esc_html($coupon_expires); ?>
									</div>
									<div class="stat-label text-xs text-gray-600"><?php esc_html_e( 'تاريخ الانتهاء', 'wp-coupon' ); ?></div>
								</div>
							</div>
						</div>
					</div>
				</article>

				<!-- Coupon Description -->
				<section class="coupon-description bg-white rounded-2xl shadow-coupon p-6 mb-8">
					<h2 class="text-2xl font-bold text-gray-900 mb-4"><?php esc_html_e( 'تفاصيل الكوبون', 'wp-coupon' ); ?></h2>
					<div class="prose prose-lg max-w-none text-gray-700 leading-relaxed">
						<?php the_content(); ?>
					</div>
				</section>
			</div>
			<?php /* OLD TABS SECTION - REPLACED WITH NEW DESIGN BELOW
			<div class="coupon-info">
				<div class="tabs">
					<div class="tab">
						<input type="radio" id="rd1" name="rd">
						<label class="tab-label" for="rd1"> طريقه تفعيل <?php echo esc_attr(get_the_title()) ?></label>
						<div class="tab-content">
							<ol>
								<li>اضغط على >> عرض الكوبون </li>
								<li>الان اضغط على نسخ <?php if (!is_tax('coupon_store')) { ?>
									<a href="<?php echo esc_attr(wpcoupon_coupon()->get_store_url()); ?>"
										rel="dofollow">
										<?php //Made by Ag 01001248698
																	$custom_taxonomy = get_the_terms(0, 'coupon_store');
																	if ($custom_taxonomy) {
																		foreach ($custom_taxonomy as $custom_tax) {
																			echo $custom_tax->name;
																		}
																	}
													?>
									</a>
									<?php } ?>
								</li>
								<li>بعد النسخ سيتم تحويلك الي المتجر المقصود <php ?> .</li>
								<li>قم ” بتسجيل الدخول ” مباشرة إذا كنت تمتلك حساب داخل المتجر.</li>
								<li>يمكنك تسجيل حساب جديد إذا كنت تريد ذلك !</li>
								<li>استخدام مربع البحث الموجود في الموقع للبحث عن المنتجات التي ترغب في شرائها.</li>
								<li> اختار المنتج الذي تريد شراءه ثم اضغط علي أضف إلي حقيبة التسوق.</li>
								<li>اذهب إلى حقيبة التسوق ثم قم بعمل لصق <?php echo esc_attr(get_the_title()) ?> الذي
									قمت بنسخه في المربع الفارغ ، ثم اضغط علي >> تفعيل الكود او القسيمة.</li>
							</ol>
							<!-- /wp:list -->

							<!-- wp:paragraph by Ag -->
							<p> <span class="has-inline-color has-luminous-vivid-orange-color"><strong>تهانينا 🥳 :
									</strong></span>سيظهر لك الخصم الذي حصلت عليه 💰</p>
							<!-- /wp:paragraph -->

							<!-- wp:paragraph by Ag -->
							<p> الآن اضغط علي >> تابع عملية الدفع ثم ادخل العنوان الذي ترغب به ثم اضغط علي ✔️ تأكيد
								الطلب وإنتظر منتجاتك حتى تصل اليك 🚐.</p>
							<!-- /wp:paragraph by Ag -->

						</div>
					</div>
					<div class="tab">
						<input type="radio" id="rd2" name="rd">
						<label class="tab-label" for="rd2">وسم الكوبون </label>
						<div class="tab-content">
							<?php echo get_the_term_list($post->ID, 'coupon_tag', '<p class="coupon-tags"><strong>' . esc_html__('الوسوم:', 'wp-coupon') . ' </strong>', ', ', '</p>'); ?>
						</div>
					</div>
					<div class="tab">
						<input type="radio" id="rd3" name="rd">
						<label for="rd3" class="tab-close">إغلاق &times;</label>
					</div>
				</div>
			*/ ?>

			<!-- Store Information Sidebar & Coupon Instructions -->
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">

				<!-- Main Content Area -->
				<div class="lg:col-span-2">
					<!-- Coupon Instructions Tabs -->
					<section class="coupon-instructions bg-white rounded-2xl shadow-coupon p-6">
						<div class="tabs-container">
							<!-- Tab Navigation -->
							<div class="tab-nav flex border-b border-gray-200 mb-6">
								<button class="tab-button active px-6 py-3 text-sm font-semibold text-secondary-600 border-b-2 border-secondary-600 transition-colors duration-200" data-tab="instructions">
									<?php esc_html_e('طريقة التفعيل', 'wp-coupon'); ?>
								</button>
								<button class="tab-button px-6 py-3 text-sm font-semibold text-gray-500 hover:text-secondary-600 border-b-2 border-transparent transition-colors duration-200" data-tab="tags">
									<?php esc_html_e('وسوم الكوبون', 'wp-coupon'); ?>
								</button>
							</div>

							<!-- Tab Content -->
							<div class="tab-content-container">
								<!-- Instructions Tab -->
								<div id="instructions-tab" class="tab-content active">
									<h3 class="text-xl font-bold text-gray-900 mb-4">
										<?php printf(esc_html__('طريقة تفعيل %s', 'wp-coupon'), get_the_title()); ?>
									</h3>
									<ol class="space-y-3 text-gray-700">
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
											<span><?php esc_html_e('اضغط على >> عرض الكوبون', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
											<span>
												<?php esc_html_e('الآن اضغط على نسخ', 'wp-coupon'); ?>
												<?php if (!is_tax('coupon_store')) { ?>
													<a href="<?php echo esc_attr(wpcoupon_coupon()->get_store_url()); ?>"
													   rel="dofollow"
													   class="text-secondary-600 hover:text-secondary-700 font-semibold transition-colors duration-200">
														<?php
														$custom_taxonomy = get_the_terms(0, 'coupon_store');
														if ($custom_taxonomy) {
															foreach ($custom_taxonomy as $custom_tax) {
																echo esc_html($custom_tax->name);
															}
														}
														?>
													</a>
												<?php } ?>
											</span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
											<span><?php esc_html_e('بعد النسخ سيتم تحويلك إلى المتجر المقصود.', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
											<span><?php esc_html_e('قم "بتسجيل الدخول" مباشرة إذا كنت تمتلك حساب داخل المتجر.', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
											<span><?php esc_html_e('يمكنك تسجيل حساب جديد إذا كنت تريد ذلك!', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">6</span>
											<span><?php esc_html_e('استخدم مربع البحث الموجود في الموقع للبحث عن المنتجات التي ترغب في شرائها.', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">7</span>
											<span><?php esc_html_e('اختر المنتج الذي تريد شراءه ثم اضغط على أضف إلى حقيبة التسوق.', 'wp-coupon'); ?></span>
										</li>
										<li class="flex items-start">
											<span class="flex-shrink-0 w-6 h-6 bg-primary-300 text-primary-900 rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">8</span>
											<span><?php printf(esc_html__('اذهب إلى حقيبة التسوق ثم قم بلصق %s الذي قمت بنسخه في المربع الفارغ، ثم اضغط على >> تفعيل الكود أو القسيمة.', 'wp-coupon'), get_the_title()); ?></span>
										</li>
									</ol>

									<!-- Success Message -->
									<div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
										<p class="text-green-800">
											<span class="font-bold">🥳 تهانينا:</span>
											<span class="mr-2">سيظهر لك الخصم الذي حصلت عليه 💰</span>
										</p>
									</div>

									<!-- Final Step -->
									<div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
										<p class="text-blue-800">
											<?php esc_html_e('الآن اضغط على >> تابع عملية الدفع ثم أدخل العنوان الذي ترغب به ثم اضغط على ✔️ تأكيد الطلب وانتظر منتجاتك حتى تصل إليك 🚐.', 'wp-coupon'); ?>
										</p>
									</div>
								</div>

								<!-- Tags Tab -->
								<div id="tags-tab" class="tab-content hidden">
									<h3 class="text-xl font-bold text-gray-900 mb-4"><?php esc_html_e('وسوم الكوبون', 'wp-coupon'); ?></h3>
									<div class="coupon-tags">
										<?php
										$tags = get_the_terms($post->ID, 'coupon_tag');
										if ($tags && !is_wp_error($tags)) {
											echo '<div class="flex flex-wrap gap-2">';
											foreach ($tags as $tag) {
												echo '<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary-100 text-secondary-800">';
												echo esc_html($tag->name);
												echo '</span>';
											}
											echo '</div>';
										} else {
											echo '<p class="text-gray-500">' . esc_html__('لا توجد وسوم لهذا الكوبون.', 'wp-coupon') . '</p>';
										}
										?>
									</div>
								</div>
							</div>
						</div>
					</section>
				</div>

				<!-- Store Information Sidebar -->
				<div class="lg:col-span-1">
					<aside class="store-sidebar">
						<!-- Store Info Card -->
						<div class="store-info-card bg-white rounded-2xl shadow-coupon p-6 mb-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4"><?php esc_html_e('معلومات المتجر', 'wp-coupon'); ?></h3>

							<?php if ($store_obj) { ?>
								<!-- Store Logo & Name -->
								<div class="store-header flex items-center mb-4">
									<div class="store-logo w-16 h-16 rounded-xl overflow-hidden bg-gray-100 mr-4">
										<?php if ($store_obj->has_thumbnail()) : ?>
											<?php echo $store_obj->get_thumbnail('medium'); ?>
										<?php else : ?>
											<div class="w-full h-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-bold text-lg">
												<?php echo esc_html(substr($store_obj->name, 0, 2)); ?>
											</div>
										<?php endif; ?>
									</div>
									<div class="store-details flex-1">
										<h4 class="font-semibold text-gray-900"><?php echo esc_html($store_obj->name); ?></h4>
										<p class="text-sm text-gray-600"><?php printf(esc_html__('%d كوبون متاح', 'wp-coupon'), $store_obj->count); ?></p>
									</div>
								</div>

								<!-- Store Actions -->
								<div class="store-actions space-y-3">
									<a href="<?php echo esc_url($coupon_obj->get_store_url()); ?>"
									   class="btn-secondary w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105">
										<?php esc_html_e('عرض جميع الكوبونات', 'wp-coupon'); ?>
									</a>
									<a href="<?php echo esc_url($store_obj->get_website_url()); ?>"
									   target="_blank"
									   class="btn-outline w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105">
										<?php esc_html_e('زيارة المتجر', 'wp-coupon'); ?>
										<svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
										</svg>
									</a>
								</div>

								<!-- Store Description -->
								<?php if ($store_obj->description) { ?>
								<div class="store-description mt-4 pt-4 border-t border-gray-100">
									<p class="text-sm text-gray-600 leading-relaxed">
										<?php echo wp_trim_words($store_obj->description, 20); ?>
									</p>
								</div>
								<?php } ?>
							<?php } ?>
						</div>

						<!-- Quick Stats -->
						<div class="quick-stats bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4"><?php esc_html_e('إحصائيات سريعة', 'wp-coupon'); ?></h3>
							<div class="stats-grid space-y-3">
								<div class="stat-item flex items-center justify-between">
									<span class="text-sm text-gray-600"><?php esc_html_e('مرات الاستخدام', 'wp-coupon'); ?></span>
									<span class="font-semibold text-secondary-600"><?php echo esc_html($total_used); ?></span>
								</div>
								<div class="stat-item flex items-center justify-between">
									<span class="text-sm text-gray-600"><?php esc_html_e('نوع الكوبون', 'wp-coupon'); ?></span>
									<span class="font-semibold text-secondary-600"><?php echo esc_html($coupon_type_text); ?></span>
								</div>
								<?php if ($discount_value) { ?>
								<div class="stat-item flex items-center justify-between">
									<span class="text-sm text-gray-600"><?php esc_html_e('قيمة الخصم', 'wp-coupon'); ?></span>
									<span class="font-semibold text-primary-600"><?php echo esc_html($discount_value); ?></span>
								</div>
								<?php } ?>
							</div>
						</div>
					</aside>
				</div>
			</div>

			<?php

			if (wpcoupon_get_option('enable_single_popular', true)) {
				// in this stores
				$number = wpcoupon_get_option('single_popular_number', 3);
				$number = absint($number);

				$custom_text = wpcoupon_get_option('');
				if (!$custom_text) {
					$custom_text = esc_html__('احدث واجدد {store}', 'wp-coupon');
				}
				$terms = get_the_terms($post->ID, 'coupon_store');
				if ($terms) {
					$current = current_time('timestamp');
					$tag_ids = wp_list_pluck($terms, 'term_id');

					$first_store = current($terms);
					$custom_text = str_replace('{store}', $first_store->name, $custom_text);
					$args = array(
						'tax_query' => array(
							'relation' => 'AND',
							array(
								'taxonomy' => 'coupon_store',
								'field' => 'term_id',
								'terms' => $tag_ids,
								'operator' => 'IN',
							),
						),
						/*
                            'meta_query'     => array(
                                'relation' => 'AND',
                                array(
                                    'relation' => 'OR',
                                    array(
                                        'key'     => '_wpc_expires',
                                        'value'   => '',
                                        'compare' => '=',
                                    ),
                                    array(
                                        'key'     => '_wpc_expires',
                                        'value'   => $current,
                                        'compare' => '>=',
                                    ),
                                )
                            ),
*/
						'post__not_in' => array($post->ID),
						'posts_per_page' => $number,
						'meta_key' => '_wpc_used',
						'orderby' => 'meta_value_num',
						'order' => 'desc'
					);

					$args = apply_filters('wpcoupon_single_popular_coupons_args', $args);

					$wp_query = new WP_Query($args);
					$max_pages =  $wp_query->max_num_pages;
					$coupons = $wp_query->get_posts();

					if ($coupons) {
						if ($custom_text) {
			?>
			<!-- Related Coupons Section -->
			<section class="related-coupons-section bg-white rounded-2xl shadow-coupon p-6 mb-8">
				<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
					<svg class="w-6 h-6 text-primary-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
					</svg>
					<?php echo wp_kses_post($custom_text); ?>
				</h2>
				<div class="related-coupons-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					<?php
						}
						$tpl_name = 'cat';
						foreach ($coupons as $post) {
							wpcoupon_setup_coupon($post);
							get_template_part('template-parts/loop/loop-coupon', $tpl_name);
						}
							?>
				</div>
			</section>


			<?php
					}
				}

				wp_reset_query();
			}
				?>

			<?php if (is_active_sidebar('new-widget-area')) : ?>
			<div id="anothor-coupons" class="new-widget-area">
				<?php dynamic_sidebar('new-widget-area'); ?>
			</div>
			<?php endif; ?>


			<!-- Comments Section -->
			<section class="single-coupon-comments bg-white rounded-2xl shadow-coupon p-6 mb-8">
				<h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
					<svg class="w-5 h-5 text-secondary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
					</svg>
					<?php esc_html_e('التعليقات والمراجعات', 'wp-coupon'); ?>
				</h3>
				<div class="comments-content">
					<?php comments_template(); ?>
				</div>
			</section>

		</main><!-- #main -->
	</div><!-- #primary -->

	<div class="confirmation">
		<span class="icon-checkmark"></span> Copied to clipboard
	</div>

</div> <!-- /#content-wrap -->


<?php get_footer(); ?>