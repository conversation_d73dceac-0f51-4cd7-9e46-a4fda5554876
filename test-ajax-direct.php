<?php
/**
 * Direct AJAX Test - Simulate the AJAX call server-side
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "<h1>Direct AJAX Test Results</h1>\n";

// Test 1: Check if coupon post type exists
echo "<h2>1. Coupon Post Type Check</h2>\n";
$coupons = get_posts(array(
    'post_type' => 'coupon',
    'posts_per_page' => 5,
    'post_status' => 'publish'
));

echo "Found " . count($coupons) . " coupons<br>\n";
if (count($coupons) > 0) {
    echo "Sample coupon IDs: ";
    foreach ($coupons as $coupon) {
        echo $coupon->ID . " ";
    }
    echo "<br>\n";
}

// Test 2: Check if wpcoupon_ajax_coupons function exists
echo "<h2>2. Function Existence Check</h2>\n";
echo "wpcoupon_ajax_coupons function exists: " . (function_exists('wpcoupon_ajax_coupons') ? 'Yes' : 'No') . "<br>\n";
echo "render_enhanced_coupon_card function exists: " . (function_exists('render_enhanced_coupon_card') ? 'Yes' : 'No') . "<br>\n";

// Test 3: Simulate AJAX call
echo "<h2>3. Simulating AJAX Call</h2>\n";

// Set up the request data
$_REQUEST['st_doing'] = 'load_coupons';
$_REQUEST['next_page'] = 2;
$_REQUEST['args'] = array(
    'layout' => '',
    'posts_per_page' => '3',
    'num_words' => '',
    'hide_expired' => ''
);

if (function_exists('wpcoupon_ajax_coupons')) {
    echo "Calling wpcoupon_ajax_coupons...<br>\n";
    
    try {
        $result = wpcoupon_ajax_coupons('load_coupons');
        
        echo "<h3>AJAX Result:</h3>\n";
        echo "<pre>";
        echo "Content length: " . strlen($result['content']) . " characters\n";
        echo "Next page: " . $result['next_page'] . "\n";
        echo "Max pages: " . $result['max_pages'] . "\n";
        echo "</pre>";
        
        if (!empty($result['content'])) {
            echo "<h3>Content Preview (first 500 chars):</h3>\n";
            echo "<pre>" . htmlspecialchars(substr($result['content'], 0, 500)) . "...</pre>\n";
            
            echo "<h3>Rendered Content:</h3>\n";
            echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 400px; overflow: auto;'>";
            echo $result['content'];
            echo "</div>\n";
        } else {
            echo "<p style='color: red;'>No content returned!</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>wpcoupon_ajax_coupons function not found!</p>\n";
}

// Test 4: Check loop template
echo "<h2>4. Loop Template Test</h2>\n";
if (count($coupons) > 0) {
    $test_coupon = $coupons[0];
    echo "Testing with coupon ID: " . $test_coupon->ID . "<br>\n";
    
    // Set up global post
    global $post;
    $post = $test_coupon;
    setup_postdata($post);
    
    echo "<h3>Loop Template Output:</h3>\n";
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
    
    ob_start();
    include 'loop/loop-coupon.php';
    $loop_output = ob_get_clean();
    
    echo $loop_output;
    echo "</div>\n";
    
    wp_reset_postdata();
}

echo "<h2>5. Enhanced Function Test</h2>\n";
if (function_exists('render_enhanced_coupon_card') && count($coupons) > 0) {
    $test_coupon = $coupons[0];
    echo "Testing enhanced card with coupon ID: " . $test_coupon->ID . "<br>\n";
    
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
    render_enhanced_coupon_card($test_coupon, 'default', array(
        'section' => 'test',
        'track_display' => false
    ));
    echo "</div>\n";
} else {
    echo "Enhanced function not available or no coupons to test<br>\n";
}

echo "<p><strong>Test completed!</strong></p>\n";
?>
