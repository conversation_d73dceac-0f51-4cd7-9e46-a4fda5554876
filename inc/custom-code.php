<?php


/**
 * Output Header Tracking Code to wp_head hook.
 */
function wpcoupon_theme_header_code() {
    $site_header_tracking = wpcoupon_get_option('site_header_tracking');
    if ( $site_header_tracking !== '' ) echo $site_header_tracking;
}
add_action( 'wp_head', 'wpcoupon_theme_header_code' );

/**
 * Output Footer Tracking Code to wp_footer hook.
 */
function wpcoupon_theme_footer_code() {
    $site_footer_tracking = wpcoupon_get_option('site_footer_tracking');
    if ( $site_footer_tracking !== '' ) echo $site_footer_tracking;
}
add_action( 'wp_footer', 'wpcoupon_theme_footer_code' );







/**
 * New Stores and coupons Rating system in in optmized schema 
 */

 we need t create and design a proffisonal and simple rating system for stores taxonami and coupon post type !! 
 use stars as svg 
 be care about RTL and LTR
 if store use coupon data from funtion 

the rating will display on the current store or coupon page will contain rate this btn and avrage stars rating ! 

the rating button on the single store page or coupon page will output the rating schema with current store or coupon data if only the current have a rating value ! 
nust be SEO optmized and performance and cach optmized with no cach issue ! 
we need option where we can output the store or coupon rating in loop card later 