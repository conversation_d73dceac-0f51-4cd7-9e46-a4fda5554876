<?php


/**
 * Output Header Tracking Code to wp_head hook.
 */
function wpcoupon_theme_header_code() {
    $site_header_tracking = wpcoupon_get_option('site_header_tracking');
    if ( $site_header_tracking !== '' ) echo $site_header_tracking;
}
add_action( 'wp_head', 'wpcoupon_theme_header_code' );

/**
 * Output Footer Tracking Code to wp_footer hook.
 */
function wpcoupon_theme_footer_code() {
    $site_footer_tracking = wpcoupon_get_option('site_footer_tracking');
    if ( $site_footer_tracking !== '' ) echo $site_footer_tracking;
}
add_action( 'wp_footer', 'wpcoupon_theme_footer_code' );







/**
 * ========================================
 * PROFESSIONAL RATING SYSTEM
 * ========================================
 *
 * Features:
 * - SVG stars with RTL/LTR support
 * - Store and Coupon rating support
 * - SEO optimized with schema markup
 * - Performance optimized with caching
 * - AJAX rating submission
 * - Loop card integration ready
 */

/**
 * Get rating data for store or coupon
 *
 * @param int $post_id Post ID
 * @param string $type 'store' or 'coupon'
 * @return array Rating data
 */
function wpcoupon_get_rating_data($post_id, $type = null) {
    // Auto-detect type if not provided
    if (!$type) {
        $post_type = get_post_type($post_id);
        $type = ($post_type === 'store') ? 'store' : 'coupon';
    }

    // Cache key for performance
    $cache_key = "wpcoupon_rating_{$type}_{$post_id}";
    $cached_data = wp_cache_get($cache_key, 'wpcoupon_ratings');

    if ($cached_data !== false) {
        return $cached_data;
    }

    // Get rating data from meta
    $total_ratings = (int) get_post_meta($post_id, '_wpcoupon_total_ratings', true);
    $total_score = (float) get_post_meta($post_id, '_wpcoupon_total_score', true);
    $average_rating = $total_ratings > 0 ? round($total_score / $total_ratings, 1) : 0;

    // Get post data based on type
    if ($type === 'store') {
        $store_data = wpcoupon_setup_store($post_id);
        $name = $store_data['name'] ?? get_the_title($post_id);
        $url = $store_data['url'] ?? get_permalink($post_id);
    } else {
        $coupon_data = wpcoupon_setup_coupon($post_id);
        $name = $coupon_data['title'] ?? get_the_title($post_id);
        $url = $coupon_data['url'] ?? get_permalink($post_id);
    }

    $rating_data = array(
        'post_id' => $post_id,
        'type' => $type,
        'name' => $name,
        'url' => $url,
        'average_rating' => $average_rating,
        'total_ratings' => $total_ratings,
        'total_score' => $total_score,
        'has_ratings' => $total_ratings > 0
    );

    // Cache for 1 hour
    wp_cache_set($cache_key, $rating_data, 'wpcoupon_ratings', HOUR_IN_SECONDS);

    return $rating_data;
}

/**
 * Generate SVG stars for rating display
 *
 * @param float $rating Rating value (0-5)
 * @param bool $interactive Whether stars are clickable
 * @param string $size Size class (small, medium, large)
 * @return string SVG stars HTML
 */
function wpcoupon_generate_rating_stars($rating, $interactive = false, $size = 'medium') {
    $rating = max(0, min(5, (float) $rating)); // Ensure rating is between 0-5
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5;
    $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

    // Size classes
    $size_classes = array(
        'small' => 'w-4 h-4',
        'medium' => 'w-5 h-5',
        'large' => 'w-6 h-6'
    );
    $star_size = $size_classes[$size] ?? $size_classes['medium'];

    // RTL support
    $is_rtl = is_rtl();
    $direction_class = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    $stars_html = '<div class="wpcoupon-rating-stars flex items-center ' . $direction_class . '" data-rating="' . esc_attr($rating) . '">';

    // Full stars
    for ($i = 1; $i <= $full_stars; $i++) {
        $interactive_attrs = $interactive ? 'data-star="' . $i . '" role="button" tabindex="0" aria-label="' . sprintf(__('Rate %d stars', 'wpcoupon'), $i) . '"' : '';
        $interactive_class = $interactive ? 'cursor-pointer hover:scale-110 transition-transform duration-200' : '';

        $stars_html .= '<svg class="' . $star_size . ' text-yellow-400 fill-current ' . $interactive_class . '" ' . $interactive_attrs . ' viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>';
    }

    // Half star
    if ($half_star) {
        $star_num = $full_stars + 1;
        $interactive_attrs = $interactive ? 'data-star="' . $star_num . '" role="button" tabindex="0" aria-label="' . sprintf(__('Rate %d stars', 'wpcoupon'), $star_num) . '"' : '';
        $interactive_class = $interactive ? 'cursor-pointer hover:scale-110 transition-transform duration-200' : '';

        $stars_html .= '<div class="relative ' . $star_size . ' ' . $interactive_class . '" ' . $interactive_attrs . '>
            <svg class="' . $star_size . ' text-gray-300 fill-current absolute" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <svg class="' . $star_size . ' text-yellow-400 fill-current absolute" style="clip-path: inset(0 50% 0 0);" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
        </div>';
    }

    // Empty stars
    for ($i = 1; $i <= $empty_stars; $i++) {
        $star_num = $full_stars + ($half_star ? 1 : 0) + $i;
        $interactive_attrs = $interactive ? 'data-star="' . $star_num . '" role="button" tabindex="0" aria-label="' . sprintf(__('Rate %d stars', 'wpcoupon'), $star_num) . '"' : '';
        $interactive_class = $interactive ? 'cursor-pointer hover:scale-110 transition-transform duration-200' : '';

        $stars_html .= '<svg class="' . $star_size . ' text-gray-300 fill-current ' . $interactive_class . '" ' . $interactive_attrs . ' viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>';
    }

    $stars_html .= '</div>';

    return $stars_html;
}

/**
 * Display rating system for single store or coupon page
 *
 * @param int $post_id Post ID (optional, uses current post if not provided)
 * @param array $options Display options
 * @return string Rating HTML
 */
function wpcoupon_display_rating_system($post_id = null, $options = array()) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    if (!$post_id) {
        return '';
    }

    // Default options
    $defaults = array(
        'show_rating_button' => true,
        'show_average' => true,
        'show_count' => true,
        'size' => 'medium',
        'context' => 'single' // 'single' or 'loop'
    );
    $options = wp_parse_args($options, $defaults);

    // Get rating data
    $rating_data = wpcoupon_get_rating_data($post_id);

    // RTL support
    $is_rtl = is_rtl();
    $text_align = $is_rtl ? 'text-right' : 'text-left';
    $flex_direction = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    $html = '<div class="wpcoupon-rating-system bg-white rounded-lg border border-gray-200 p-4 mb-6" data-post-id="' . esc_attr($post_id) . '" data-type="' . esc_attr($rating_data['type']) . '">';

    // Rating header
    $html .= '<div class="flex items-center justify-between mb-4 ' . $flex_direction . '">';
    $html .= '<h3 class="text-lg font-bold text-gray-900 ' . $text_align . '">';
    $html .= $rating_data['type'] === 'store' ? __('تقييم المتجر', 'wpcoupon') : __('تقييم الكوبون', 'wpcoupon');
    $html .= '</h3>';

    if ($options['show_average'] && $rating_data['has_ratings']) {
        $html .= '<div class="flex items-center space-x-2 space-x-reverse">';
        $html .= '<span class="text-2xl font-bold text-yellow-600">' . number_format($rating_data['average_rating'], 1) . '</span>';
        $html .= '<span class="text-gray-500">/5</span>';
        $html .= '</div>';
    }
    $html .= '</div>';

    // Stars display
    $html .= '<div class="flex items-center justify-between mb-4 ' . $flex_direction . '">';
    $html .= '<div class="flex items-center space-x-3 space-x-reverse">';
    $html .= wpcoupon_generate_rating_stars($rating_data['average_rating'], false, $options['size']);

    if ($options['show_count'] && $rating_data['has_ratings']) {
        $html .= '<span class="text-sm text-gray-600 ' . ($is_rtl ? 'mr-3' : 'ml-3') . '">';
        $html .= sprintf(_n('تقييم واحد', '%s تقييم', $rating_data['total_ratings'], 'wpcoupon'), number_format($rating_data['total_ratings']));
        $html .= '</span>';
    }
    $html .= '</div>';
    $html .= '</div>';

    // Rating button
    if ($options['show_rating_button']) {
        $html .= '<div class="border-t border-gray-200 pt-4">';
        $html .= '<h4 class="text-md font-semibold text-gray-800 mb-3 ' . $text_align . '">' . __('قيم هذا', 'wpcoupon') . ' ' . ($rating_data['type'] === 'store' ? __('المتجر', 'wpcoupon') : __('الكوبون', 'wpcoupon')) . '</h4>';

        $html .= '<div class="flex items-center justify-between ' . $flex_direction . '">';
        $html .= '<div class="wpcoupon-interactive-rating" data-post-id="' . esc_attr($post_id) . '">';
        $html .= wpcoupon_generate_rating_stars(0, true, $options['size']);
        $html .= '</div>';

        $html .= '<button type="button" class="wpcoupon-submit-rating bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>';
        $html .= __('إرسال التقييم', 'wpcoupon');
        $html .= '</button>';
        $html .= '</div>';

        // Rating message
        $html .= '<div class="wpcoupon-rating-message mt-3 text-sm hidden"></div>';
        $html .= '</div>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Display compact rating for loop cards
 *
 * @param int $post_id Post ID
 * @param string $size Star size (small, medium, large)
 * @return string Compact rating HTML
 */
function wpcoupon_display_loop_rating($post_id, $size = 'small') {
    $rating_data = wpcoupon_get_rating_data($post_id);

    if (!$rating_data['has_ratings']) {
        return '';
    }

    $is_rtl = is_rtl();
    $flex_direction = $is_rtl ? 'flex-row-reverse' : 'flex-row';

    $html = '<div class="wpcoupon-loop-rating flex items-center space-x-1 space-x-reverse ' . $flex_direction . '">';
    $html .= wpcoupon_generate_rating_stars($rating_data['average_rating'], false, $size);
    $html .= '<span class="text-xs text-gray-600 ' . ($is_rtl ? 'mr-2' : 'ml-2') . '">';
    $html .= number_format($rating_data['average_rating'], 1) . ' (' . number_format($rating_data['total_ratings']) . ')';
    $html .= '</span>';
    $html .= '</div>';

    return $html;
}

/**
 * Generate schema markup for ratings
 *
 * @param int $post_id Post ID
 * @return string Schema markup JSON-LD
 */
function wpcoupon_generate_rating_schema($post_id) {
    $rating_data = wpcoupon_get_rating_data($post_id);

    if (!$rating_data['has_ratings']) {
        return '';
    }

    $schema = array(
        '@context' => 'https://schema.org',
        '@type' => $rating_data['type'] === 'store' ? 'Store' : 'Offer',
        'name' => $rating_data['name'],
        'url' => $rating_data['url'],
        'aggregateRating' => array(
            '@type' => 'AggregateRating',
            'ratingValue' => $rating_data['average_rating'],
            'reviewCount' => $rating_data['total_ratings'],
            'bestRating' => 5,
            'worstRating' => 1
        )
    );

    // Add additional schema properties based on type
    if ($rating_data['type'] === 'store') {
        $store_data = wpcoupon_setup_store($post_id);
        if (!empty($store_data['description'])) {
            $schema['description'] = wp_strip_all_tags($store_data['description']);
        }
    } else {
        $coupon_data = wpcoupon_setup_coupon($post_id);
        if (!empty($coupon_data['description'])) {
            $schema['description'] = wp_strip_all_tags($coupon_data['description']);
        }
        if (!empty($coupon_data['discount_value'])) {
            $schema['priceSpecification'] = array(
                '@type' => 'PriceSpecification',
                'price' => $coupon_data['discount_value'],
                'priceCurrency' => 'SAR'
            );
        }
    }

    return '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . '</script>';
}

/**
 * AJAX handler for rating submission
 */
function wpcoupon_submit_rating_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wpcoupon_rating_nonce')) {
        wp_die(__('Security check failed', 'wpcoupon'));
    }

    $post_id = (int) $_POST['post_id'];
    $rating = (int) $_POST['rating'];
    $user_ip = $_SERVER['REMOTE_ADDR'];

    // Validate rating
    if ($rating < 1 || $rating > 5) {
        wp_send_json_error(__('تقييم غير صحيح', 'wpcoupon'));
    }

    // Check if user already rated (by IP)
    $user_ratings = get_post_meta($post_id, '_wpcoupon_user_ratings', true);
    if (!is_array($user_ratings)) {
        $user_ratings = array();
    }

    if (isset($user_ratings[$user_ip])) {
        wp_send_json_error(__('لقد قمت بالتقييم مسبقاً', 'wpcoupon'));
    }

    // Add new rating
    $user_ratings[$user_ip] = array(
        'rating' => $rating,
        'timestamp' => current_time('timestamp')
    );

    // Update meta
    update_post_meta($post_id, '_wpcoupon_user_ratings', $user_ratings);

    // Update totals
    $total_ratings = (int) get_post_meta($post_id, '_wpcoupon_total_ratings', true);
    $total_score = (float) get_post_meta($post_id, '_wpcoupon_total_score', true);

    $new_total_ratings = $total_ratings + 1;
    $new_total_score = $total_score + $rating;
    $new_average = round($new_total_score / $new_total_ratings, 1);

    update_post_meta($post_id, '_wpcoupon_total_ratings', $new_total_ratings);
    update_post_meta($post_id, '_wpcoupon_total_score', $new_total_score);

    // Clear cache
    wp_cache_delete("wpcoupon_rating_store_{$post_id}", 'wpcoupon_ratings');
    wp_cache_delete("wpcoupon_rating_coupon_{$post_id}", 'wpcoupon_ratings');

    wp_send_json_success(array(
        'message' => __('شكراً لك! تم إرسال تقييمك بنجاح', 'wpcoupon'),
        'new_average' => $new_average,
        'new_count' => $new_total_ratings
    ));
}
add_action('wp_ajax_wpcoupon_submit_rating', 'wpcoupon_submit_rating_ajax');
add_action('wp_ajax_nopriv_wpcoupon_submit_rating', 'wpcoupon_submit_rating_ajax');

/**
 * Enqueue rating system scripts and styles
 */
function wpcoupon_enqueue_rating_scripts() {
    if (is_singular(array('store', 'coupon'))) {
        wp_localize_script('jquery', 'wpcoupon_rating', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wpcoupon_rating_nonce'),
            'messages' => array(
                'select_rating' => __('يرجى اختيار تقييم', 'wpcoupon'),
                'error' => __('حدث خطأ، يرجى المحاولة مرة أخرى', 'wpcoupon'),
                'success' => __('تم إرسال تقييمك بنجاح', 'wpcoupon')
            )
        ));

        // Add inline JavaScript for rating functionality
        wp_add_inline_script('jquery', '
        jQuery(document).ready(function($) {
            var selectedRating = 0;

            // Handle star clicks
            $(document).on("click", ".wpcoupon-interactive-rating svg", function() {
                selectedRating = parseInt($(this).data("star"));
                var $container = $(this).closest(".wpcoupon-interactive-rating");

                // Update visual state
                $container.find("svg").each(function(index) {
                    if (index < selectedRating) {
                        $(this).removeClass("text-gray-300").addClass("text-yellow-400");
                    } else {
                        $(this).removeClass("text-yellow-400").addClass("text-gray-300");
                    }
                });

                // Enable submit button
                $container.closest(".wpcoupon-rating-system").find(".wpcoupon-submit-rating").prop("disabled", false);
            });

            // Handle rating submission
            $(document).on("click", ".wpcoupon-submit-rating", function() {
                if (selectedRating === 0) {
                    alert(wpcoupon_rating.messages.select_rating);
                    return;
                }

                var $button = $(this);
                var $system = $button.closest(".wpcoupon-rating-system");
                var postId = $system.data("post-id");

                $button.prop("disabled", true).text("جاري الإرسال...");

                $.ajax({
                    url: wpcoupon_rating.ajax_url,
                    type: "POST",
                    data: {
                        action: "wpcoupon_submit_rating",
                        post_id: postId,
                        rating: selectedRating,
                        nonce: wpcoupon_rating.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $system.find(".wpcoupon-rating-message")
                                .removeClass("hidden text-red-600")
                                .addClass("text-green-600")
                                .text(response.data.message);

                            // Update display
                            location.reload();
                        } else {
                            $system.find(".wpcoupon-rating-message")
                                .removeClass("hidden text-green-600")
                                .addClass("text-red-600")
                                .text(response.data);
                            $button.prop("disabled", false).text("إرسال التقييم");
                        }
                    },
                    error: function() {
                        $system.find(".wpcoupon-rating-message")
                            .removeClass("hidden text-green-600")
                            .addClass("text-red-600")
                            .text(wpcoupon_rating.messages.error);
                        $button.prop("disabled", false).text("إرسال التقييم");
                    }
                });
            });
        });
        ');
    }
}
add_action('wp_enqueue_scripts', 'wpcoupon_enqueue_rating_scripts');

/**
 * Output schema markup in head for single pages
 */
function wpcoupon_output_rating_schema() {
    if (is_singular(array('store', 'coupon'))) {
        echo wpcoupon_generate_rating_schema(get_the_ID());
    }
}
add_action('wp_head', 'wpcoupon_output_rating_schema');

/**
 * Clear rating cache when post is updated
 */
function wpcoupon_clear_rating_cache($post_id) {
    $post_type = get_post_type($post_id);
    if (in_array($post_type, array('store', 'coupon'))) {
        wp_cache_delete("wpcoupon_rating_store_{$post_id}", 'wpcoupon_ratings');
        wp_cache_delete("wpcoupon_rating_coupon_{$post_id}", 'wpcoupon_ratings');
    }
}
add_action('save_post', 'wpcoupon_clear_rating_cache');

/**
 * Helper function to easily display rating in templates
 *
 * Usage:
 * - Single page: echo wpcoupon_rating();
 * - Loop card: echo wpcoupon_rating($post_id, 'loop');
 * - Custom: echo wpcoupon_rating($post_id, 'custom', array('size' => 'large'));
 */
function wpcoupon_rating($post_id = null, $context = 'single', $options = array()) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    if (!$post_id) {
        return '';
    }

    switch ($context) {
        case 'loop':
            return wpcoupon_display_loop_rating($post_id, $options['size'] ?? 'small');

        case 'single':
        default:
            return wpcoupon_display_rating_system($post_id, $options);
    }
}

/**
 * ========================================
 * USAGE EXAMPLES:
 * ========================================
 *
 * 1. Display full rating system on single store/coupon page:
 *    echo wpcoupon_rating();
 *
 * 2. Display compact rating in loop cards:
 *    echo wpcoupon_rating($post_id, 'loop');
 *
 * 3. Display rating with custom options:
 *    echo wpcoupon_rating($post_id, 'single', array(
 *        'show_rating_button' => false,
 *        'size' => 'large'
 *    ));
 *
 * 4. Get rating data programmatically:
 *    $rating_data = wpcoupon_get_rating_data($post_id);
 *
 * 5. Generate schema markup:
 *    echo wpcoupon_generate_rating_schema($post_id);
 */

