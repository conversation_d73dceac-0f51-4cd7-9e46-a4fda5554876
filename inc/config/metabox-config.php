<?php
/**
 * Metabox config file
 *
 * @package AGS Coupon/inc/config
 * @license  http://www.opensource.org/licenses/gpl-license.php GPL v2.0 (or later)
 * @link     https://github.com/webdevstudios/Custom-Metaboxes-and-Fields-for-WordPress
 */

/**
 * Get the bootstrap!
 */
if ( file_exists(  get_template_directory() . '/inc/metabox/init.php' ) ) {
	require_once  get_template_directory() . '/inc/metabox/init.php';
    require_once  get_template_directory() . '/inc/metabox-addons/extra-types.php';
    require_once  get_template_directory() . '/inc/metabox-addons/icon/icon.php';
}


function cmb2_change_minutes_step( $l10n ){
    $l10n['defaults']['time_picker']['stepMinute'] = 1;
    return $l10n;
}

add_filter( 'cmb2_localized_data', 'cmb2_change_minutes_step' );


/**
 * Sanitizes WYSIWYG fields like WordPress does for post_content fields.
 */
function cmb2_html_content_sanitize( $content ) {
    return apply_filters( 'content_save_pre', $content );
}



/**
 * Metabox for Show on page IDs callback
 * <AUTHOR> Morton
 * @link https://github.com/WebDevStudios/CMB2/wiki/Adding-your-own-show_on-filters
 *
 * @param bool $display
 * @param array $meta_box
 * @return bool display metabox
 */
function wpcoupon_metabox_show_on_cb( $field ) {
    global $post;

    $meta_box = $field->args;
    if ( ! isset( $meta_box['show_on_page'] ) ) {
        return true ;
    }

    $post_id = $post->ID;

    if ( ! $post_id ) {
        return false;
    }

    // See if there's a match
    return in_array( $post_id, (array) $meta_box['show_on_page'] );
}



add_action( 'cmb2_init', 'wpcoupon_coupon_meta_boxes' );
add_action( 'cmb2_init', 'wpcoupon_page_meta_boxes' );

/**
 * Add metabox for coupon
 * @since 1.0.0
 */
function wpcoupon_coupon_meta_boxes() {
    // Start with an underscore to hide fields from custom fields list
    $prefix = '_wpc_';

    $coupon_meta = new_cmb2_box( array(
        'id'            => $prefix . 'coupon',
        'title'         => esc_html__( 'اعدادات الكوبون', 'wp-coupon' ),
        'object_types'  => array( 'coupon', ), // Post type
        // 'show_on_cb' => 'yourprefix_show_if_front_page', // function should return a bool value
        // 'context'    => 'normal',
        // 'priority'   => 'high',
        // 'show_names' => true, // Show field names on the left
        // 'cmb_styles' => false, // false to disable the CMB stylesheet
        // 'closed'     => true, // true to keep the metabox closed by default
    ) );


    $coupon_meta->add_field( array(
        'name'             => esc_html__( 'نوع الصفقه', 'wp-coupon' ),
        'id'               => $prefix . 'coupon_type',
        'type'             => 'select',
        'show_option_none' => false,
        'options'          => wpcoupon_get_coupon_types(),
    ) );


    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'رمز الكوبون', 'wp-coupon' ),
        'id'            => $prefix . 'coupon_type_code',
        'type'          => 'text_medium',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'مثال: EMIAXHGF', 'wp-coupon' ),
        ),
        'before_row'    => '<div class="st-condition-field cmb-row" data-show-when = "code" data-show-on="' . $prefix . 'coupon_type' . '">',
        'after_row'     => '</div>'

    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'صوره الكوبون القابله للطباعه', 'wp-coupon' ),
        'id'            => $prefix . 'coupon_type_printable',
        'type'          => 'file',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'http://...', 'wp-coupon' ),
        ),
        'before_row'    => '<div class="st-condition-field cmb-row" data-show-when = "print" data-show-on="' . $prefix . 'coupon_type' . '">',
        'after_row'     => '</div>'
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'رابط الإحاله', 'wp-coupon' ),
        'id'            => $prefix . 'destination_url',
        'type'          => 'text_url',
        'desc'          => esc_html__( 'اذا تركته فارغ فسيتم استخدام رابط الإحاله من المتجر التابع له هذا الكوبون', 'wp-coupon' ),
        'attributes'    => array(
            'placeholder'   => esc_html__( 'http://...', 'wp-coupon' ),
        ),
    ) );

    $coupon_meta->add_field( array(
        'name'       => esc_html__( 'ينتهي في ', 'wp-coupon' ),
        'id'         => $prefix . 'expires',
        'type'       => 'text_datetime_timestamp',
        'desc'       => esc_html__( 'وقت ابتداء الكوبون', 'wp-coupon' ),
    ) );

    $coupon_meta->add_field( array(
        'name'       => esc_html__( 'وقت البدايه', 'wp-coupon' ),
        'id'         => $prefix . 'start_on',
        'type'       => 'text_datetime_timestamp',
        'desc'       => esc_html__( 'وقت انتهاء الكوبون', 'wp-coupon' ),
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'قيمه التخفيض', 'wp-coupon' ),
        'id'            => $prefix . 'coupon_save',
        'type'          => 'text_medium',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'مثال : 15% تخفيض', 'wp-coupon' ),
        ),
        'desc'          => esc_html__( 'This text maybe display as coupon thumbnail.', 'wp-coupon' ),
        'before_row'    => '<div class="st-condition-field cmb-row" data-show-when = "code" data-show-on="' . $prefix . 'coupon_type' . '">',
        'after_row'     => '</div>'
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'شحن مجاني', 'wp-coupon' ),
        'desc'          => esc_html__( 'هل يشمل هذا الكوبون الشحن المجاني', 'wp-coupon' ),
        'id'            => $prefix . 'free_shipping',
        'type'          => 'checkbox'
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'حصري', 'wp-coupon' ),
        'desc'          => esc_html__( 'اجعل الكوبون حصري', 'wp-coupon' ),
        'id'            => $prefix . 'exclusive',
        'type'          => 'checkbox'
    ) );


    // Custom tracking
    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'عدد مرات استخدام الكوبون ', 'wp-coupon' ),
        'desc'          => esc_html__( 'عدد مرات استخدام الكوبون  الافتراضيه', 'wp-coupon' ),
        'id'            => $prefix . 'used',
        'type'          => 'text'
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'عدد مرات مشاهدات الكوبون ', 'wp-coupon' ),
        'desc'          => esc_html__( 'عدد مرات مشاهدات الكوبون الافتراضيه', 'wp-coupon' ),
        'id'            => $prefix . 'views',
        'type'          => 'text'
    ) );
    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'Vote Up', 'wp-coupon' ),
        'desc'          => esc_html__( '', 'wp-coupon' ),
        'id'            => $prefix . 'vote_up',
        'type'          => 'text'
    ) );

    $coupon_meta->add_field( array(
        'name'          => esc_html__( 'Vote Down', 'wp-coupon' ),
        'desc'          => esc_html__( '', 'wp-coupon' ),
        'id'            => $prefix . 'vote_down',
        'type'          => 'text'
    ) );
    


}



/**
 * Add meta box for pages
 */
function wpcoupon_page_meta_boxes() {
    // Start with an underscore to hide fields from custom fields list
    $prefix = '_wpc_';

    $page_meta = new_cmb2_box( array(
        'id'            => $prefix . 'page',
        'title'         => esc_html__( 'اعدادات الصفحه', 'wp-coupon' ),
        'object_types'  => array( 'page' ), // Post type
    ) );

    $page_meta->add_field( array(
        'name'          => esc_html__( 'العنوان الثانوي', 'wp-coupon' ),
        'id'            => $prefix . 'custom_title',
        'desc'          => esc_html__( 'هذا العنوان خاص بالسيو (SEO) يظهر فقط داخل الصفحه.', 'wp-coupon' ),
        'type'          => 'text_medium',
    ) );

}



add_action( 'cmb2_admin_init', 'wpcoupon_register_coupon_store_taxonomy_metabox' );
/**
 * Hook in and add a metabox to add fields to taxonomy terms
 */
function wpcoupon_register_coupon_store_taxonomy_metabox() {
    $prefix = '_wpc_';

    /**
     * Metabox to add fields to coupon store
     */
    $store_meta = new_cmb2_box( array(
        'id'               => $prefix . 'store_meta',
        'title'            => esc_html__( 'بيانات المتجر', 'wp-coupon' ),
        'object_types'     => array( 'term' ), // Tells CMB2 to use term_meta vs post_meta
        'taxonomies'       => array( 'coupon_store' ), // Tells CMB2 which taxonomies should have these fields
        'new_term_section' => true, // Will display in the "Add New Category" section
    ) );

	$store_meta->add_field( array(
        'name'          => esc_html__( 'رابط المتجر', 'wp-coupon' ),
        'id'            => $prefix . 'store_url',
        'desc'          => esc_html__( 'رابط عنوان المتجر', 'wp-coupon' ),
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'http://example.com', 'wp-coupon' ),
        ),
    ) );

    $store_meta->add_field( array(
        'name'          => esc_html__( 'رابط الإحاله', 'wp-coupon' ),
        'id'            => $prefix . 'store_aff_url',
        'desc'          => esc_html__( 'Affiliate URL.', 'wp-coupon' ),
        'type'          => 'text_url',
        'attributes'    => array(
            'placeholder'   => esc_html__( 'http://example.com', 'wp-coupon' ),
        ),
    ) );
  

    $store_meta->add_field( array(
        'name'    => esc_html__( 'صوره المتجر ', 'wp-coupon' ),
        'desc'          => esc_html__( 'يجب ان تكون 200*200 فيما اعلي', 'wp-coupon' ),
        'id'      => $prefix . 'store_image',
        'type'    => 'file',
        // Optional:
        'options' => array(
            'url' => false, // Hide the text input for the url
            'add_upload_file_text' => 'رفع صوره' // Change upload button text. Default: "Add or Upload File"
        ),
    ) );

	$store_meta->add_field( array(
        'name'          => esc_html__( 'العنوان الثانوي', 'wp-coupon' ),
        'id'            => $prefix . 'store_heading',
        'desc'          => esc_html__( 'عنوان خاص بالسيو (SEO) الثانوي يظهر داخل صفحه المتجر فقط.', 'wp-coupon' ),
        'type'          => 'text_medium',
        'sanitization_cb'    => 'cmb2_html_content_sanitize'
    ) );

    $store_meta->add_field( array(
        'name'          => esc_html__( 'اجعل المتجر في المتاجر البارزه', 'wp-coupon' ),
        'desc'          => esc_html__( 'قم بتفعيل هذا الخيار اذا كنت تريد اظهار هذا المتجر في المتاجر البارزة.', 'wp-coupon' ),
        'id'            => $prefix . 'is_featured',
        'type'          => 'checkbox'
    ) );


	$store_meta->add_field( array(
        'name'     => esc_html__( 'بيانات اضافيه عن المتجر', 'wp-coupon' ),
        'desc'     => esc_html__( 'هذا القسم يظهر اسفل الكوبونات داخل صفحه المتجر فقط.', 'wp-coupon' ),
        'id'       => $prefix . 'extra_info',
        'type'     => 'wysiwyg',
        'options' => array(
            'wpautop' => true, // use wpautop?
            'media_buttons' => true, // show insert/upload button(s)
            ///'textarea_name' => $editor_id, // set the textarea name to something different, square brackets [] can be used here
            'textarea_rows' => get_option('default_post_edit_rows', 6), // rows="..."
            'tabindex' => '',
            'editor_css' => '', // intended for extra styles for both visual and HTML editors buttons, needs to include the `<style>` tags, can use "scoped".
            'editor_class' => '', // add extra class(es) to the editor textarea
            'teeny' => false, // output the minimal editor config used in Press This
            'dfw' => false, // replace the default fullscreen with DFW (needs specific css)
            'tinymce' => true, // load TinyMCE, can be used to pass settings directly to TinyMCE using an array()
            'quicktags' => true // load Quicktags, can be used to pass settings directly to Quicktags using an array()
        ),
        'on_front' => true,
    )
                     

);


    /**
     * Metabox to add fields to Coupon categories
     */
    $cat_meta = new_cmb2_box( array(
        'id'               => $prefix . 'coupon_category_meta',
        'title'            => esc_html__( 'بيانات التصنيف', 'wp-coupon' ),
        'object_types'     => array( 'term' ), // Tells CMB2 to use term_meta vs post_meta
        'taxonomies'       => array( 'coupon_category' ), // Tells CMB2 which taxonomies should have these fields
        // 'new_term_section' => true, // Will display in the "Add New Category" section
    ) );

    $cat_meta->add_field( array(
        'name'          => esc_html__( 'شعار التصنيف', 'wp-coupon' ),
        'id'            => $prefix . 'icon',
        'type'          => 'icon',
        'desc'          => 'Category icon',
    ) );


    $cat_meta->add_field( array(
        'name'    => esc_html__( 'الصوره المصغره', 'wp-coupon' ),
        'desc'    => 'The image use as thumbnail on single category page',
        'id'      => $prefix . 'cat_image',
        'type'    => 'file',
        // Optional:
        'options' => array(
            'url' => false, // Hide the text input for the url
            //'add_upload_file_text' => 'Add File' // Change upload button text. Default: "Add or Upload File"
        ),
    ) );


}
