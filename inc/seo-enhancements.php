<?php
/**
 * SEO and Performance Enhancements for Ag-Coupon Theme
 *
 * This file contains SEO optimizations, structured data,
 * and performance enhancements for better search engine visibility
 * and user experience.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enhanced SEO Meta Tags
 */
function wpcoupon_enhanced_meta_tags() {
    global $post;

    // Basic meta tags
    echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">' . "\n";
    echo '<meta name="format-detection" content="telephone=no">' . "\n";
    echo '<meta name="theme-color" content="#FCD34D">' . "\n";
    echo '<meta name="msapplication-TileColor" content="#FCD34D">' . "\n";

    // Open Graph and Twitter Cards
    if (is_singular('coupon')) {
        wpcoupon_setup_coupon($post);
        $coupon = wpcoupon_coupon();
        $store = wpcoupon_store();
        $store_name = $store ? $store->get_display_name() : '';
        $description = $coupon->post_excerpt ?: wp_trim_words($coupon->post_content, 30);
        $image = $coupon->get_thumb('large', false, true);

        echo '<meta property="og:type" content="product">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($coupon->post_title) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr(wp_trim_words($description, 30)) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_permalink()) . '">' . "\n";

        if ($image) {
            echo '<meta property="og:image" content="' . esc_url($image) . '">' . "\n";
            echo '<meta property="og:image:width" content="1200">' . "\n";
            echo '<meta property="og:image:height" content="630">' . "\n";
        }

        // Twitter Cards
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($coupon->post_title) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr(wp_trim_words($description, 30)) . '">' . "\n";

        if ($image) {
            echo '<meta name="twitter:image" content="' . esc_url($image) . '">' . "\n";
        }

    } elseif (is_tax('coupon_store')) {
        $term = get_queried_object();
        wpcoupon_setup_store($term);
        $store = wpcoupon_store();
        $description = $store->description;
        $image = $store->get_thumbnail('large', true);

        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($store->get_display_name() . ' Coupons & Deals') . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($description ?: 'Find the best coupons and deals for ' . $store->get_display_name()) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_term_link($term)) . '">' . "\n";

        if ($image) {
            echo '<meta property="og:image" content="' . esc_url($image) . '">' . "\n";
        }
    }
}
add_action('wp_head', 'wpcoupon_enhanced_meta_tags', 1);

/**
 * Structured Data for Coupons
 */
function wpcoupon_structured_data() {
    global $post;

    if (is_singular('coupon')) {
        wpcoupon_setup_coupon($post);
        $coupon = wpcoupon_coupon();
        $store = wpcoupon_store();
        $store_name = $store ? $store->get_display_name() : '';
        $description = $coupon->post_excerpt ?: wp_trim_words($coupon->post_content, 50);
        $expiry_date = $coupon->get_expires();
        $code = $coupon->get_code();

        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'Offer',
            'name' => $coupon->post_title,
            'description' => wp_trim_words($description, 50),
            'url' => get_permalink(),
            'seller' => array(
                '@type' => 'Organization',
                'name' => $store_name
            ),
            'priceSpecification' => array(
                '@type' => 'PriceSpecification',
                'priceCurrency' => 'USD',
                'price' => '0'
            ),
            'availability' => $coupon->has_expired() ? 'https://schema.org/OutOfStock' : 'https://schema.org/InStock'
        );

        if ($expiry_date && !$coupon->has_expired()) {
            $structured_data['validThrough'] = date('Y-m-d', strtotime($expiry_date));
        }

        if ($code) {
            $structured_data['promoCode'] = $code;
        }

        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";

    } elseif (is_tax('coupon_store')) {
        $term = get_queried_object();
        wpcoupon_setup_store($term);
        $store = wpcoupon_store();

        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $store->get_display_name(),
            'description' => $store->description ?: 'Coupons and deals for ' . $store->get_display_name(),
            'url' => get_term_link($term),
            'sameAs' => array()
        );

        $website = $store->get_website_url();
        if ($website) {
            $structured_data['sameAs'][] = $website;
        }

        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";

    } elseif (is_home() || is_front_page()) {
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'description' => get_bloginfo('description'),
            'url' => home_url(),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => array(
                    '@type' => 'EntryPoint',
                    'urlTemplate' => home_url('/?s={search_term_string}')
                ),
                'query-input' => 'required name=search_term_string'
            )
        );

        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
}
add_action('wp_head', 'wpcoupon_structured_data', 2);

/**
 * Performance Optimizations
 */
function wpcoupon_performance_optimizations() {
    // Preload critical resources
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/css/style.css" as="style">' . "\n";

    // DNS prefetch for external resources
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">' . "\n";

    // Preconnect to critical third-party origins
    echo '<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>' . "\n";
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";

    // Resource hints for better performance
    if (is_singular('coupon')) {
        global $post;
        wpcoupon_setup_coupon($post);
        $coupon = wpcoupon_coupon();
        $destination_url = $coupon->get_destination_url();
        if ($destination_url) {
            $parsed_url = parse_url($destination_url);
            if (isset($parsed_url['host'])) {
                echo '<link rel="dns-prefetch" href="//' . esc_attr($parsed_url['host']) . '">' . "\n";
            }
        }
    }
}
add_action('wp_head', 'wpcoupon_performance_optimizations', 0);

/**
 * Enhanced Breadcrumbs with Structured Data
 */
function wpcoupon_enhanced_breadcrumbs() {
    if (is_front_page()) {
        return;
    }

    $breadcrumbs = array();
    $breadcrumbs[] = array(
        'name' => esc_html__('Home', 'wp-coupon'),
        'url' => home_url()
    );

    if (is_singular('coupon')) {
        global $post;
        wpcoupon_setup_coupon($post);
        $coupon = wpcoupon_coupon();
        $store = wpcoupon_store();

        $breadcrumbs[] = array(
            'name' => esc_html__('Coupons', 'wp-coupon'),
            'url' => get_post_type_archive_link('coupon')
        );

        if ($store) {
            $breadcrumbs[] = array(
                'name' => $store->get_display_name(),
                'url' => $store->get_url()
            );
        }

        $breadcrumbs[] = array(
            'name' => $coupon->post_title,
            'url' => get_permalink()
        );

    } elseif (is_tax('coupon_store')) {
        $term = get_queried_object();
        wpcoupon_setup_store($term);
        $store = wpcoupon_store();

        $breadcrumbs[] = array(
            'name' => esc_html__('Stores', 'wp-coupon'),
            'url' => get_post_type_archive_link('coupon')
        );

        $breadcrumbs[] = array(
            'name' => $store->get_display_name(),
            'url' => get_term_link($term)
        );
    }

    // Output breadcrumb HTML
    if (count($breadcrumbs) > 1) {
        echo '<nav class="breadcrumb" aria-label="' . esc_attr__('Breadcrumb navigation', 'wp-coupon') . '">';
        echo '<ol class="flex items-center space-x-2 text-sm text-gray-600">';

        foreach ($breadcrumbs as $index => $breadcrumb) {
            echo '<li class="flex items-center">';

            if ($index > 0) {
                echo '<span class="breadcrumb-separator mx-2" aria-hidden="true">/</span>';
            }

            if ($index === count($breadcrumbs) - 1) {
                echo '<span class="text-gray-900 font-medium" aria-current="page">' . esc_html($breadcrumb['name']) . '</span>';
            } else {
                echo '<a href="' . esc_url($breadcrumb['url']) . '" class="hover:text-primary-600 transition-colors duration-200">' . esc_html($breadcrumb['name']) . '</a>';
            }

            echo '</li>';
        }

        echo '</ol>';
        echo '</nav>';

        // Structured data for breadcrumbs
        $structured_breadcrumbs = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array()
        );

        foreach ($breadcrumbs as $index => $breadcrumb) {
            $structured_breadcrumbs['itemListElement'][] = array(
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            );
        }

        echo '<script type="application/ld+json">' . wp_json_encode($structured_breadcrumbs, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
    }
}

/**
 * Image Lazy Loading Enhancement
 */
function wpcoupon_enhance_image_lazy_loading($attr, $attachment, $size) {
    if (!isset($attr['loading'])) {
        $attr['loading'] = 'lazy';
    }

    if (!isset($attr['decoding'])) {
        $attr['decoding'] = 'async';
    }

    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'wpcoupon_enhance_image_lazy_loading', 10, 3);

/**
 * Critical CSS Inlining for Above-the-Fold Content
 */
function wpcoupon_inline_critical_css() {
    $critical_css = '
    .site-header{backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px)}
    .coupon-card{transform:translateZ(0);backface-visibility:hidden}
    .btn-primary,.btn-secondary{transform:translateZ(0);backface-visibility:hidden}
    .search-input{font-size:16px}
    .skip-links a:focus{position:static;width:auto;height:auto;left:auto;top:auto;overflow:visible}
    ';

    echo '<style id="critical-css">' . $critical_css . '</style>' . "\n";
}
add_action('wp_head', 'wpcoupon_inline_critical_css', 1);
