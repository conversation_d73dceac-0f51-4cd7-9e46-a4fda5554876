<?php
/**
 * Modern Coupon Card System
 * Creative modern design with branded yellow colors
 * Uses functions from inc/core/coupon.php
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * THE ONLY COUPON CARD FUNCTION - Modern Creative Design
 * Uses existing functions from inc/core/coupon.php
 *
 * @param WP_Post|int $coupon Coupon post object or ID
 * @param string $style 'featured' or 'default'
 * @param array $options Card display options
 */
function render_coupon_card($coupon, $style = 'default', $options = array()) {
    // Debug: Add comprehensive error handling
    try {
        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG && defined('DOING_AJAX') && DOING_AJAX) {
            error_log('Modern Coupon Card: Starting render for coupon: ' . (is_object($coupon) ? $coupon->ID : $coupon));
        }

        // Get coupon post
        if (is_numeric($coupon)) {
            $coupon = get_post($coupon);
        }

        if (!$coupon || $coupon->post_type !== 'coupon') {
            if (defined('WP_DEBUG') && WP_DEBUG && defined('DOING_AJAX') && DOING_AJAX) {
                error_log('Modern Coupon Card: Invalid coupon object');
            }
            return;
        }

    // Default options
    $defaults = array(
        'show_image' => true,
        'show_store_logo' => true,
        'show_description' => true,
        'show_badges' => true,
        'show_voting' => true,
        'show_stats' => true,
        'description_length' => 15
    );
    $options = wp_parse_args($options, $defaults);

    // Setup coupon using existing theme function AFTER setting options
    wpcoupon_setup_coupon($coupon);
    $coupon_obj = wpcoupon_coupon();

    if (!$coupon_obj) {
        // Fallback: create coupon object directly
        if (class_exists('WPCoupon_Coupon')) {
            $coupon_obj = new WPCoupon_Coupon($coupon->ID);
        } else {
            return; // Can't create coupon object
        }
    }

    // Get coupon data using existing theme functions
    $coupon_type = method_exists($coupon_obj, 'get_type') ? $coupon_obj->get_type() : 'code';
    $coupon_code = method_exists($coupon_obj, 'get_code') ? $coupon_obj->get_code() : '';
    $is_exclusive = method_exists($coupon_obj, 'is_exclusive') ? $coupon_obj->is_exclusive() : false;
    $destination_url = method_exists($coupon_obj, 'get_destination_url') ? $coupon_obj->get_destination_url() : get_permalink($coupon->ID);
    $coupon_href = method_exists($coupon_obj, 'get_href') ? $coupon_obj->get_href() : get_permalink($coupon->ID);
    
    // Get meta data directly (simplified - no expiration, no printable)
    $coupon_save = get_post_meta($coupon->ID, '_wpc_coupon_save', true);
    $free_shipping = get_post_meta($coupon->ID, '_wpc_free_shipping', true);
    $used_count = get_post_meta($coupon->ID, '_wpc_used', true) ?: 0;
    $vote_up = get_post_meta($coupon->ID, '_wpc_vote_up', true) ?: 0;
    $vote_down = get_post_meta($coupon->ID, '_wpc_vote_down', true) ?: 0;

    // Get store information using existing functions with error handling
    $store = null;
    $store_name = '';
    $store_image = '';

    try {
        if (isset($coupon_obj->store) && $coupon_obj->store) {
            $store = $coupon_obj->store;
            $store_name = method_exists($store, 'get_display_name') ? $store->get_display_name() : '';
            $store_image = method_exists($store, 'get_thumbnail') ? $store->get_thumbnail() : '';
        }
    } catch (Exception $e) {
        // Fallback: get store from taxonomy
        $store_terms = get_the_terms($coupon->ID, 'coupon_store');
        if ($store_terms && !is_wp_error($store_terms)) {
            $store_term = $store_terms[0];
            $store_name = $store_term->name;
            $store_image = get_term_meta($store_term->term_id, '_wpc_store_image', true);
        }
    }

    // Image fallback: Featured Image -> Store Image -> Placeholder
    $coupon_image = '';
    if ($options['show_image']) {
        $featured_image = get_the_post_thumbnail_url($coupon->ID, 'medium');
        if ($featured_image) {
            $coupon_image = $featured_image;
        } elseif ($store_image) {
            $coupon_image = $store_image;
        }
    }

    // Success rate calculation
    $total_votes = $vote_up + $vote_down;
    $success_rate = $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 95;

    // Card classes with modern design
    $card_classes = array(
        'modern-coupon-card',
        'group',
        'relative',
        'bg-white',
        'rounded-2xl',
        'border',
        'border-gray-100',
        'overflow-hidden',
        'transition-all',
        'duration-500',
        'hover:shadow-2xl',
        'hover:-translate-y-2',
        'hover:border-yellow-300'
    );

    // Add style-specific classes
    if ($style === 'featured') {
        $card_classes[] = 'featured-coupon';
        $card_classes[] = 'ring-3';
        $card_classes[] = 'ring-yellow-400';
        $card_classes[] = 'shadow-lg';
        $card_classes[] = 'bg-gradient-to-br';
        $card_classes[] = 'from-yellow-50';
        $card_classes[] = 'to-white';
    } else {
        $card_classes[] = 'default-coupon';
    }

    // Add coupon type class for CSS filtering
    $card_classes[] = 'coupon-type-' . $coupon_type;

        // Check if we're in AJAX context and use simple fallback
        if (defined('DOING_AJAX') && DOING_AJAX) {
            // Simple AJAX fallback - render directly without complex template
            echo '<article class="' . esc_attr(implode(' ', $card_classes)) . '" data-coupon-id="' . $coupon->ID . '" data-coupon-type="' . esc_attr($coupon_type) . '">';

            // Simple image
            if ($options['show_image'] && $coupon_image) {
                echo '<div class="relative h-48 overflow-hidden">';
                echo '<img src="' . esc_url($coupon_image) . '" alt="' . esc_attr($coupon->post_title) . '" class="w-full h-full object-cover">';

                // Simple badges
                if ($options['show_badges']) {
                    echo '<div class="absolute top-4 right-4 flex flex-col space-y-2">';
                    if ($is_exclusive) echo '<div class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">⭐ حصري</div>';
                    if ($free_shipping) echo '<div class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">🚚 شحن مجاني</div>';
                    if ($coupon_save) echo '<div class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">💰 ' . esc_html($coupon_save) . '</div>';
                    echo '</div>';
                }

                echo '</div>';
            }

            echo '<div class="p-6">';

            // Store info
            if ($options['show_store_logo'] && $store_name) {
                echo '<div class="flex items-center mb-4">';
                echo '<div class="w-12 h-12 rounded-xl bg-gray-200 flex items-center justify-center mr-3">';
                if ($store_image) {
                    echo '<img src="' . esc_url($store_image) . '" alt="' . esc_attr($store_name) . '" class="w-full h-full object-cover rounded-xl">';
                } else {
                    echo '<span class="text-sm font-bold">' . esc_html(substr($store_name, 0, 2)) . '</span>';
                }
                echo '</div>';
                echo '<div><p class="font-bold">' . esc_html($store_name) . '</p>';
                if ($options['show_stats']) echo '<p class="text-xs text-gray-500">' . $success_rate . '% نجاح • ' . $used_count . ' استخدام</p>';
                echo '</div></div>';
            }

            // Title
            echo '<h3 class="text-lg font-bold mb-3">';
            echo '<a href="' . esc_url($coupon_href) . '" class="coupon-title-link coupon-button" data-type="' . esc_attr($coupon_type) . '" data-coupon-id="' . $coupon->ID . '" data-aff-url="' . esc_attr($destination_url) . '" data-code="' . esc_attr($coupon_code) . '">';
            echo esc_html($coupon->post_title);
            echo '</a></h3>';

            // Description
            if ($options['show_description']) {
                $description = $coupon->post_excerpt ?: $coupon->post_content;
                echo '<p class="text-sm text-gray-600 mb-4">' . esc_html(wp_trim_words($description, $options['description_length'], '...')) . '</p>';
            }

            // Action section
            echo '<div class="flex items-center justify-between mb-4">';

            // Voting
            if ($options['show_voting']) {
                echo '<div class="flex items-center space-x-3">';
                echo '<button class="vote-up flex items-center" data-coupon-id="' . $coupon->ID . '"><span class="text-xs">' . $vote_up . '</span> 👍</button>';
                echo '<button class="vote-down flex items-center" data-coupon-id="' . $coupon->ID . '"><span class="text-xs">' . $vote_down . '</span> 👎</button>';
                echo '</div>';
            }

            // Button
            if ($coupon_type === 'code' && $coupon_code) {
                echo '<a href="' . esc_url($coupon_href) . '" class="coupon-button bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-sm" data-coupon-id="' . $coupon->ID . '" data-type="code" data-aff-url="' . esc_attr($destination_url) . '" data-code="' . esc_attr($coupon_code) . '">عرض الكود</a>';
            } else {
                echo '<a href="' . esc_url($coupon_href) . '" class="coupon-button bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-sm" data-coupon-id="' . $coupon->ID . '" data-type="sale" data-aff-url="' . esc_attr($destination_url) . '">احصل على الصفقة</a>';
            }

            echo '</div>';
            echo '</div>';
            echo '</article>';

        } else {
            // Normal context - use full template
            $template_path = get_template_directory() . '/template-parts/components/modern-coupon-card.php';

            if (file_exists($template_path)) {
                include $template_path;
            } else {
                throw new Exception('Template file not found: ' . $template_path);
            }
        }

    } catch (Exception $e) {
        // Log the error for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Modern Coupon Card Error: ' . $e->getMessage());
        }

        // Fallback: Simple HTML output for AJAX
        echo '<div class="bg-white p-4 rounded-lg border">';
        echo '<h3 class="font-bold">' . esc_html($coupon->post_title) . '</h3>';
        echo '<p class="text-sm text-gray-600">' . esc_html(wp_trim_words($coupon->post_excerpt, 15)) . '</p>';
        echo '<a href="' . esc_url(get_permalink($coupon->ID)) . '" class="inline-block mt-2 bg-blue-500 text-white px-4 py-2 rounded">عرض الكوبون</a>';
        echo '</div>';
    }
}

/**
 * Legacy wrapper functions for backward compatibility
 */
function wpcoupon_render_coupon_card($coupon, $style = 'default', $args = array()) {
    // Handle legacy boolean style parameter
    if (is_bool($style)) {
        $style = $style ? 'featured' : 'default';
    }
    render_coupon_card($coupon, $style, $args);
}

function wpcoupon_render_featured_coupon_card($coupon) {
    render_coupon_card($coupon, 'featured');
}

function wpcoupon_render_default_coupon_card($coupon) {
    render_coupon_card($coupon, 'default');
}

function render_enhanced_coupon_card($coupon, $style = 'default', $args = array()) {
    render_coupon_card($coupon, $style, $args);
}
