<?php
/**
 * Modern Coupon Card System
 * Creative modern design with branded yellow colors
 * Uses functions from inc/core/coupon.php
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * THE ONLY COUPON CARD FUNCTION - Modern Creative Design
 * Uses existing functions from inc/core/coupon.php
 *
 * @param WP_Post|int $coupon Coupon post object or ID
 * @param string $style 'featured' or 'default'
 * @param array $options Card display options
 */
function render_coupon_card($coupon, $style = 'default', $options = array()) {
    // Get coupon post
    if (is_numeric($coupon)) {
        $coupon = get_post($coupon);
    }
    
    if (!$coupon || $coupon->post_type !== 'coupon') {
        return;
    }

    // Default options
    $defaults = array(
        'show_image' => true,
        'show_store_logo' => true,
        'show_description' => true,
        'show_badges' => true,
        'show_voting' => true,
        'show_stats' => true,
        'description_length' => 15
    );
    $options = wp_parse_args($options, $defaults);

    // Setup coupon using existing theme function AFTER setting options
    wpcoupon_setup_coupon($coupon);
    $coupon_obj = wpcoupon_coupon();

    if (!$coupon_obj) {
        // Fallback: create coupon object directly
        if (class_exists('WPCoupon_Coupon')) {
            $coupon_obj = new WPCoupon_Coupon($coupon->ID);
        } else {
            return; // Can't create coupon object
        }
    }

    // Get coupon data using existing theme functions
    $coupon_type = method_exists($coupon_obj, 'get_type') ? $coupon_obj->get_type() : 'code';
    $coupon_code = method_exists($coupon_obj, 'get_code') ? $coupon_obj->get_code() : '';
    $is_exclusive = method_exists($coupon_obj, 'is_exclusive') ? $coupon_obj->is_exclusive() : false;
    $destination_url = method_exists($coupon_obj, 'get_destination_url') ? $coupon_obj->get_destination_url() : get_permalink($coupon->ID);
    $coupon_href = method_exists($coupon_obj, 'get_href') ? $coupon_obj->get_href() : get_permalink($coupon->ID);
    
    // Get meta data directly (simplified - no expiration, no printable)
    $coupon_save = get_post_meta($coupon->ID, '_wpc_coupon_save', true);
    $free_shipping = get_post_meta($coupon->ID, '_wpc_free_shipping', true);
    $used_count = get_post_meta($coupon->ID, '_wpc_used', true) ?: 0;
    $vote_up = get_post_meta($coupon->ID, '_wpc_vote_up', true) ?: 0;
    $vote_down = get_post_meta($coupon->ID, '_wpc_vote_down', true) ?: 0;

    // Get store information using existing functions with error handling
    $store = null;
    $store_name = '';
    $store_image = '';

    try {
        if (isset($coupon_obj->store) && $coupon_obj->store) {
            $store = $coupon_obj->store;
            $store_name = method_exists($store, 'get_display_name') ? $store->get_display_name() : '';
            $store_image = method_exists($store, 'get_thumbnail') ? $store->get_thumbnail() : '';
        }
    } catch (Exception $e) {
        // Fallback: get store from taxonomy
        $store_terms = get_the_terms($coupon->ID, 'coupon_store');
        if ($store_terms && !is_wp_error($store_terms)) {
            $store_term = $store_terms[0];
            $store_name = $store_term->name;
            $store_image = get_term_meta($store_term->term_id, '_wpc_store_image', true);
        }
    }

    // Image fallback: Featured Image -> Store Image -> Placeholder
    $coupon_image = '';
    if ($options['show_image']) {
        $featured_image = get_the_post_thumbnail_url($coupon->ID, 'medium');
        if ($featured_image) {
            $coupon_image = $featured_image;
        } elseif ($store_image) {
            $coupon_image = $store_image;
        }
    }

    // Success rate calculation
    $total_votes = $vote_up + $vote_down;
    $success_rate = $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 95;

    // Card classes with modern design
    $card_classes = array(
        'modern-coupon-card',
        'group',
        'relative',
        'bg-white',
        'rounded-2xl',
        'border',
        'border-gray-100',
        'overflow-hidden',
        'transition-all',
        'duration-500',
        'hover:shadow-2xl',
        'hover:-translate-y-2',
        'hover:border-yellow-300'
    );

    // Add style-specific classes
    if ($style === 'featured') {
        $card_classes[] = 'featured-coupon';
        $card_classes[] = 'ring-3';
        $card_classes[] = 'ring-yellow-400';
        $card_classes[] = 'shadow-lg';
        $card_classes[] = 'bg-gradient-to-br';
        $card_classes[] = 'from-yellow-50';
        $card_classes[] = 'to-white';
    } else {
        $card_classes[] = 'default-coupon';
    }

    // Add coupon type class for CSS filtering
    $card_classes[] = 'coupon-type-' . $coupon_type;

    // Include the template
    include get_template_directory() . '/template-parts/components/modern-coupon-card.php';
}

/**
 * Legacy wrapper functions for backward compatibility
 */
function wpcoupon_render_coupon_card($coupon, $style = 'default', $args = array()) {
    // Handle legacy boolean style parameter
    if (is_bool($style)) {
        $style = $style ? 'featured' : 'default';
    }
    render_coupon_card($coupon, $style, $args);
}

function wpcoupon_render_featured_coupon_card($coupon) {
    render_coupon_card($coupon, 'featured');
}

function wpcoupon_render_default_coupon_card($coupon) {
    render_coupon_card($coupon, 'default');
}

function render_enhanced_coupon_card($coupon, $style = 'default', $args = array()) {
    render_coupon_card($coupon, $style, $args);
}
