<?php
/**
 * Coupon Card Component Helper Functions
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Render a modern minimal coupon card - simplified version
 *
 * @param WP_Post|object $coupon Coupon object or post
 * @param bool $is_featured Whether this is a featured coupon (default: false)
 */
function wpcoupon_render_coupon_card($coupon, $is_featured = false) {
    // Set up coupon context
    wpcoupon_setup_coupon($coupon);

    // Set query vars for the component
    set_query_var('coupon_card_is_featured', $is_featured);

    // Render the component
    get_template_part('template-parts/components/coupon-card-enhanced');

    // Clean up query vars
    set_query_var('coupon_card_is_featured', null);
}

/**
 * Render default coupon card
 *
 * @param WP_Post|object $coupon Coupon object or post
 */
function wpcoupon_render_default_coupon_card($coupon) {
    wpcoupon_render_coupon_card($coupon, false);
}

/**
 * Render featured coupon card
 *
 * @param WP_Post|object $coupon Coupon object or post
 */
function wpcoupon_render_featured_coupon_card($coupon) {
    wpcoupon_render_coupon_card($coupon, true);
}

// Removed complex preset system - keeping only default and featured options

/**
 * Integration with existing theme AJAX functions
 * Uses the existing wpcoupon_coupon_ajax function for load more functionality
 */

/**
 * Override get_template_part to always use enhanced coupon cards
 * Ensures unified card system across all contexts
 */
function wpcoupon_override_coupon_template_part($template, $slug, $name) {
    // Check if this is a coupon loop template (both old and new paths)
    if ($slug === 'loop/loop-coupon' || $slug === 'template-parts/loop/loop-coupon') {
        // Always use enhanced card system for consistency

        // Use enhanced coupon card with proper grid wrapper
        echo '<div class="coupon-grid-item">';
        if (function_exists('render_enhanced_coupon_card')) {
            // Always use default style for AJAX load more - featured style only for explicitly featured coupons
            $style = 'default';
            render_enhanced_coupon_card(get_post(), $style, array(
                'section' => 'override-' . ($name ?: 'default'),
                'track_display' => false,
                'strict_deduplication' => false,
                'force_render' => true,
                'show_store_logo' => true,
                'show_badges' => true,
                'show_description' => true,
                'description_length' => 15,
                'scratch_effect' => false,
                'hover_effects' => true
            ));
        } else {
            // Fallback to helper function
            wpcoupon_render_default_coupon_card(get_post());
        }
        echo '</div>';
        return ''; // Return empty to prevent original template loading
    }

    return $template;
}
add_filter('get_template_part_loop/loop-coupon', 'wpcoupon_override_coupon_template_part', 10, 3);
add_filter('get_template_part_template-parts/loop/loop-coupon', 'wpcoupon_override_coupon_template_part', 10, 3);

/**
 * Add modern card support to AJAX args
 */
function wpcoupon_add_modern_card_to_ajax_args($args) {
    // Add preset parameter to AJAX requests
    if (isset($_REQUEST['preset'])) {
        $args['preset'] = sanitize_text_field($_REQUEST['preset']);
    }
    return $args;
}
add_filter('wpcoupon_ajax_args', 'wpcoupon_add_modern_card_to_ajax_args');

// Removed old AJAX render function - using simplified approach

/**
 * Render multiple coupon cards with optional load more button
 *
 * @param array $coupons Array of coupon objects
 * @param array $args Configuration options
 */
function wpcoupon_render_coupons_with_load_more($coupons, $args = array()) {
    $defaults = array(
        'container_id' => 'coupons-grid',
        'container_class' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4',
        'show_load_more' => true,
        'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon'),
        'loading_text' => esc_html__('جاري التحميل...', 'wp-coupon'),
        'doing' => 'load_coupons',
        'next_page' => 2,
        'preset' => 'modern',
        'featured_cards' => false,
        'max_pages' => 0,
        'current_page' => 1,
        'per_page' => 8,
        'category_id' => 0,
        'store_id' => 0,
        'filter_type' => '',
    );

    $args = wp_parse_args($args, $defaults);

    // Start container
    echo '<div class="' . esc_attr($args['container_class']) . '" id="' . esc_attr($args['container_id']) . '">';

    // Render coupon cards
    if (!empty($coupons)) {
        foreach ($coupons as $coupon) {
            wpcoupon_render_coupon_card($coupon, $args['featured_cards']);
        }
    }

    echo '</div>';

    // Render load more button if enabled and there are more pages
    if ($args['show_load_more'] && $args['next_page'] > 0 && $args['max_pages'] > $args['current_page']) {
        wpcoupon_render_load_more_button($args);
    }
}

/**
 * Get load more button HTML for integration with existing AJAX system
 *
 * @param array $args Button configuration
 * @return string Button HTML
 */
function wpcoupon_get_load_more_button($args = array()) {
    $defaults = array(
        'text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon'),
        'loading_text' => esc_html__('جاري التحميل...', 'wp-coupon'),
        'class' => 'wpc-load-more btn-secondary',
        'preset' => 'modern',
        'posts_per_page' => 8,
        'container_id' => 'latest-coupons-grid',
        'doing' => 'load_coupons',
        'next_page' => 2,
        'category_id' => 0,
        'store_id' => 0,
        'filter_type' => '',
    );

    $args = wp_parse_args($args, $defaults);

    ob_start();
    ?>
    <div class="load-more-container mt-12 mb-8">
        <div class="flex justify-center">
            <div class="load-more">
                <button type="button"
                       class="load-more-btn inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                       data-doing="<?php echo esc_attr($args['doing']); ?>"
                       data-next-page="<?php echo esc_attr($args['next_page']); ?>"
                       data-loading-text="<?php echo esc_attr($args['loading_text']); ?>"
                       data-preset="<?php echo esc_attr($args['preset']); ?>"
                       data-container="<?php echo esc_attr($args['container_id']); ?>"
                       <?php if ($args['category_id']) : ?>data-cat-id="<?php echo esc_attr($args['category_id']); ?>"<?php endif; ?>
                       <?php if ($args['store_id']) : ?>data-store-id="<?php echo esc_attr($args['store_id']); ?>"<?php endif; ?>
                       <?php if ($args['filter_type']) : ?>data-filter-type="<?php echo esc_attr($args['filter_type']); ?>"<?php endif; ?>>

                    <!-- Loading Spinner (Hidden by default) -->
                    <svg class="loading-spinner animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>

                    <!-- Button Text -->
                    <span class="btn-text flex items-center">
                        <?php echo esc_html($args['text']); ?>
                        <svg class="btn-icon w-5 h-5 ml-2 transform transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </span>
                </button>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div class="mt-4 text-center">
            <div class="inline-flex items-center text-sm text-gray-500">
                <span class="loading-progress hidden">
                    <svg class="animate-pulse w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    جاري تحميل المزيد من الكوبونات...
                </span>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Render load more button directly (echoes output)
 *
 * @param array $args Button configuration
 */
function wpcoupon_render_load_more_button($args = array()) {
    echo wpcoupon_get_load_more_button($args);
}

/**
 * Get pagination data for load more functionality
 *
 * @param WP_Query $query WordPress query object
 * @param int $current_page Current page number
 * @return array Pagination data
 */
function wpcoupon_get_pagination_data($query = null, $current_page = 1) {
    global $wp_query;

    if (!$query) {
        $query = $wp_query;
    }

    $max_pages = $query->max_num_pages;
    $next_page = ($current_page < $max_pages) ? $current_page + 1 : 0;
    $has_more = $current_page < $max_pages;

    return array(
        'current_page' => $current_page,
        'max_pages' => $max_pages,
        'next_page' => $next_page,
        'has_more' => $has_more,
        'total_posts' => $query->found_posts,
        'posts_per_page' => $query->query_vars['posts_per_page']
    );
}
