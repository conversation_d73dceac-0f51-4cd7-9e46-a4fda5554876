<?php
/**
 * Simplified Store Functions
 * Clean and simple approach for store display
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get featured stores
 *
 * @param int $number Number of stores to get
 * @return array Array of store objects
 */
function get_featured_stores($number = 12) {
    return wpcoupon_get_featured_stores($number);
}

/**
 * Get all stores (non-featured)
 *
 * @param int $number Number of stores to get
 * @param array $exclude Array of store IDs to exclude
 * @return array Array of store objects
 */
function get_all_stores($number = 12, $exclude = array()) {
    $args = array(
        'number' => $number,
        'orderby' => 'count',
        'order' => 'DESC',
        'hide_empty' => true
    );

    if (!empty($exclude)) {
        $args['exclude'] = $exclude;
    }

    return wpcoupon_get_stores($args);
}

/**
 * Get stores by IDs
 *
 * @param array $ids Array of store IDs
 * @return array Array of store objects
 */
function get_stores_by_ids($ids = array()) {
    if (empty($ids)) {
        return array();
    }

    return wpcoupon_get_stores(array(
        'include' => $ids,
        'hide_empty' => false
    ));
}

/**
 * Render store card - simplified version
 *
 * @param object $store Store object
 * @param string $style Card style: 'featured', 'slider', 'grid', 'minimal'
 * @param array $args Additional arguments
 */
function render_store_card($store, $style = 'featured', $args = array()) {
    if (!$store) return;

    // Set up store context
    wpcoupon_setup_store($store);

    // Default arguments
    $defaults = array(
        'show_badge' => true,
        'badge_text' => '',
        'show_coupons_count' => true,
        'link_target' => '_self'
    );

    $args = wp_parse_args($args, $defaults);

    // Set badge text based on style
    if (empty($args['badge_text'])) {
        switch ($style) {
            case 'featured':
                $args['badge_text'] = esc_html__('مميز', 'wp-coupon');
                break;
            case 'slider':
                $args['badge_text'] = esc_html__('مميز', 'wp-coupon');
                break;
            default:
                $args['badge_text'] = '';
                $args['show_badge'] = false;
                break;
        }
    }

    // Set query vars for the template
    set_query_var('store_card_style', $style);
    set_query_var('store_card_args', $args);

    // Load the appropriate template
    get_template_part('template-parts/components/simple-store-card');

    // Clean up
    set_query_var('store_card_style', null);
    set_query_var('store_card_args', null);
}

/**
 * Render featured stores slider
 *
 * @param array $stores Array of store objects
 * @param array $slider_args Slider configuration
 */
function render_featured_stores_slider($stores, $slider_args = array()) {
    if (empty($stores)) return;

    // Default slider configuration
    $defaults = array(
        'slides_per_view' => 4,
        'slides_per_view_mobile' => 1,
        'slides_per_view_tablet' => 2,
        'gap' => 24,
        'autoplay' => true,
        'autoplay_delay' => 4000,
        'loop' => true,
        'pagination' => true,
        'navigation' => true
    );

    $slider_args = wp_parse_args($slider_args, $defaults);

    // Generate unique ID for this slider
    $slider_id = 'featured-stores-slider-' . uniqid();

    // Check if RTL
    $is_rtl = is_rtl();
    $direction = $is_rtl ? 'rtl' : 'ltr';
    ?>

    <!-- Featured Stores Slider -->
    <div class="featured-stores-slider-container" dir="<?php echo esc_attr($direction); ?>">
        <div id="<?php echo esc_attr($slider_id); ?>" class="splide featured-stores-slider <?php echo esc_attr($direction); ?>">
            <div class="splide__track">
                <ul class="splide__list">
                    <?php foreach ($stores as $store) : ?>
                        <li class="splide__slide">
                            <?php render_store_card($store, 'slider'); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Navigation arrows -->
            <div class="splide__arrows">
                <button class="splide__arrow splide__arrow--prev" aria-label="<?php echo $is_rtl ? esc_attr__('الشريحة التالية', 'wp-coupon') : esc_attr__('Previous slide', 'wp-coupon'); ?>">
                    <?php if ($is_rtl) : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    <?php else : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    <?php endif; ?>
                </button>
                <button class="splide__arrow splide__arrow--next" aria-label="<?php echo $is_rtl ? esc_attr__('الشريحة السابقة', 'wp-coupon') : esc_attr__('Next slide', 'wp-coupon'); ?>">
                    <?php if ($is_rtl) : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    <?php else : ?>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    <?php endif; ?>
                </button>
            </div>

            <!-- Pagination dots -->
            <div class="splide__pagination"></div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Splide !== 'undefined') {
            new Splide('#<?php echo esc_js($slider_id); ?>', {
                type: '<?php echo $slider_args['loop'] ? 'loop' : 'slide'; ?>',
                direction: '<?php echo $direction; ?>',
                perPage: <?php echo intval($slider_args['slides_per_view']); ?>,
                perMove: 1,
                gap: '<?php echo intval($slider_args['gap']); ?>px',
                autoplay: <?php echo $slider_args['autoplay'] ? 'true' : 'false'; ?>,
                interval: <?php echo intval($slider_args['autoplay_delay']); ?>,
                pagination: <?php echo $slider_args['pagination'] ? 'true' : 'false'; ?>,
                arrows: <?php echo $slider_args['navigation'] ? 'true' : 'false'; ?>,
                <?php if ($is_rtl) : ?>
                i18n: {
                    prev: '<?php echo esc_js(__('الشريحة السابقة', 'wp-coupon')); ?>',
                    next: '<?php echo esc_js(__('الشريحة التالية', 'wp-coupon')); ?>',
                    first: '<?php echo esc_js(__('الشريحة الأولى', 'wp-coupon')); ?>',
                    last: '<?php echo esc_js(__('الشريحة الأخيرة', 'wp-coupon')); ?>',
                    slideX: '<?php echo esc_js(__('الشريحة %s', 'wp-coupon')); ?>',
                    pageX: '<?php echo esc_js(__('الصفحة %s', 'wp-coupon')); ?>',
                    play: '<?php echo esc_js(__('تشغيل', 'wp-coupon')); ?>',
                    pause: '<?php echo esc_js(__('إيقاف مؤقت', 'wp-coupon')); ?>'
                },
                <?php endif; ?>
                breakpoints: {
                    768: {
                        perPage: <?php echo intval($slider_args['slides_per_view_mobile']); ?>,
                        gap: '16px'
                    },
                    1024: {
                        perPage: <?php echo intval($slider_args['slides_per_view_tablet']); ?>,
                        gap: '20px'
                    }
                }
            }).mount();
        }
    });
    </script>
    <?php
}

/**
 * Render stores grid
 *
 * @param array $stores Array of store objects
 * @param string $style Grid style: 'featured', 'grid', 'minimal'
 * @param array $grid_args Grid configuration
 */
function render_stores_grid($stores, $style = 'grid', $grid_args = array()) {
    if (empty($stores)) return;

    // Default grid configuration
    $defaults = array(
        'columns' => 4,
        'columns_tablet' => 2,
        'columns_mobile' => 1,
        'gap' => 6,
        'container_class' => 'stores-grid-container'
    );

    $grid_args = wp_parse_args($grid_args, $defaults);

    $grid_classes = sprintf(
        'grid grid-cols-%d md:grid-cols-%d lg:grid-cols-%d gap-%d',
        $grid_args['columns_mobile'],
        $grid_args['columns_tablet'],
        $grid_args['columns'],
        $grid_args['gap']
    );
    ?>

    <div class="<?php echo esc_attr($grid_args['container_class']); ?>">
        <div class="<?php echo esc_attr($grid_classes); ?>">
            <?php foreach ($stores as $store) : ?>
                <div class="store-grid-item">
                    <?php render_store_card($store, $style); ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

/**
 * Shortcode for displaying stores
 *
 * Usage examples:
 * [stores type="featured" number="8"]
 * [stores type="all" number="12" exclude="1,2,3"]
 * [stores type="ids" ids="1,2,3,4,5"]
 * [stores type="featured" display="slider" slides="4"]
 * [stores type="all" display="grid" columns="3"]
 */
function stores_shortcode($atts) {
    $atts = shortcode_atts(array(
        'type' => 'featured',           // featured, all, ids
        'number' => 8,                  // number of stores
        'ids' => '',                    // comma-separated store IDs
        'exclude' => '',                // comma-separated store IDs to exclude
        'display' => 'grid',            // grid, slider
        'style' => 'featured',          // featured, grid, minimal
        'columns' => 4,                 // grid columns
        'slides' => 4,                  // slider slides per view
        'autoplay' => 'true',           // slider autoplay
        'loop' => 'true',               // slider loop
        'gap' => 24                     // gap between items
    ), $atts);

    // Get stores based on type
    $stores = array();

    switch ($atts['type']) {
        case 'featured':
            $stores = get_featured_stores(intval($atts['number']));
            break;

        case 'all':
            $exclude_ids = !empty($atts['exclude']) ?
                array_map('intval', explode(',', $atts['exclude'])) : array();
            $stores = get_all_stores(intval($atts['number']), $exclude_ids);
            break;

        case 'ids':
            if (!empty($atts['ids'])) {
                $store_ids = array_map('intval', explode(',', $atts['ids']));
                $stores = get_stores_by_ids($store_ids);
            }
            break;
    }

    if (empty($stores)) {
        return '<p class="no-stores-message">' . esc_html__('لا توجد متاجر للعرض', 'wp-coupon') . '</p>';
    }

    // Start output buffering
    ob_start();

    // Render based on display type
    if ($atts['display'] === 'slider') {
        render_featured_stores_slider($stores, array(
            'slides_per_view' => intval($atts['slides']),
            'autoplay' => $atts['autoplay'] === 'true',
            'loop' => $atts['loop'] === 'true',
            'gap' => intval($atts['gap'])
        ));
    } else {
        render_stores_grid($stores, $atts['style'], array(
            'columns' => intval($atts['columns']),
            'gap' => intval($atts['gap'])
        ));
    }

    return ob_get_clean();
}
add_shortcode('stores', 'stores_shortcode');
