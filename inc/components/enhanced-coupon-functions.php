<?php
/**
 * Enhanced Coupon Functions with Deduplication Logic
 * Modern coupon card system with comprehensive duplicate prevention
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Global coupon tracking system to prevent duplicates across the entire page
 */
class AG_Coupon_Tracker {
    private static $instance = null;
    private $displayed_coupons = array();
    private $page_sections = array();

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Track a coupon as displayed
     */
    public function track_coupon($coupon_id, $section = 'default') {
        $this->displayed_coupons[] = intval($coupon_id);
        if (!isset($this->page_sections[$section])) {
            $this->page_sections[$section] = array();
        }
        $this->page_sections[$section][] = intval($coupon_id);
    }

    /**
     * Check if coupon is already displayed
     */
    public function is_coupon_displayed($coupon_id) {
        return in_array(intval($coupon_id), $this->displayed_coupons);
    }

    /**
     * Get all displayed coupon IDs
     */
    public function get_displayed_coupons() {
        return array_unique($this->displayed_coupons);
    }

    /**
     * Get coupons displayed in specific section
     */
    public function get_section_coupons($section) {
        return isset($this->page_sections[$section]) ? $this->page_sections[$section] : array();
    }

    /**
     * Reset tracking (useful for testing or specific cases)
     */
    public function reset() {
        $this->displayed_coupons = array();
        $this->page_sections = array();
    }

    /**
     * Get statistics
     */
    public function get_stats() {
        return array(
            'total_displayed' => count($this->displayed_coupons),
            'unique_coupons' => count(array_unique($this->displayed_coupons)),
            'sections' => array_keys($this->page_sections),
            'duplicates_prevented' => count($this->displayed_coupons) - count(array_unique($this->displayed_coupons))
        );
    }
}

/**
 * Get featured coupons with deduplication support
 *
 * @param int $number Number of coupons to get
 * @param bool $exclude_displayed Whether to exclude already displayed coupons
 * @param string $section Section identifier for tracking
 * @return array Array of coupon objects
 */
function get_featured_coupons($number = 8, $exclude_displayed = true, $section = 'featured') {
    $tracker = AG_Coupon_Tracker::get_instance();

    // Get base featured coupons (exclusive coupons)
    $args = array(
        'post_type' => 'coupon',
        'posts_per_page' => $number * 2, // Get more to account for filtering
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_wpc_exclusive',
                'value' => 'on',
                'compare' => '='
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    );

    $coupons = get_posts($args);

    if ($exclude_displayed && !empty($coupons)) {
        $filtered_coupons = array();
        foreach ($coupons as $coupon) {
            if (!$tracker->is_coupon_displayed($coupon->ID)) {
                $filtered_coupons[] = $coupon;
                if (count($filtered_coupons) >= $number) {
                    break;
                }
            }
        }
        $coupons = $filtered_coupons;
    }

    // Limit to requested number
    if (count($coupons) > $number) {
        $coupons = array_slice($coupons, 0, $number);
    }

    return $coupons;
}

/**
 * Get all coupons (non-featured) with enhanced deduplication
 *
 * @param int $number Number of coupons to get
 * @param array $exclude Array of coupon IDs to exclude
 * @param bool $exclude_displayed Whether to exclude already displayed coupons
 * @param string $section Section identifier for tracking
 * @return array Array of coupon objects
 */
function get_all_coupons($number = 8, $exclude = array(), $exclude_displayed = true, $section = 'latest') {
    $tracker = AG_Coupon_Tracker::get_instance();

    // Combine manual exclude list with displayed coupons
    $exclude_ids = is_array($exclude) ? $exclude : array();

    if ($exclude_displayed) {
        $displayed_coupons = $tracker->get_displayed_coupons();
        $exclude_ids = array_merge($exclude_ids, $displayed_coupons);
    }

    $args = array(
        'post_type' => 'coupon',
        'posts_per_page' => $number * 2, // Get more to account for filtering
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    );

    if (!empty($exclude_ids)) {
        $args['post__not_in'] = array_unique($exclude_ids);
    }

    // Exclude featured coupons
    $args['meta_query'] = array(
        'relation' => 'OR',
        array(
            'key' => '_wpc_exclusive',
            'compare' => 'NOT EXISTS'
        ),
        array(
            'key' => '_wpc_exclusive',
            'value' => 'on',
            'compare' => '!='
        )
    );

    $coupons = get_posts($args);

    // Limit to requested number
    if (count($coupons) > $number) {
        $coupons = array_slice($coupons, 0, $number);
    }

    return $coupons;
}

/**
 * Get coupons by IDs with deduplication support
 *
 * @param array $ids Array of coupon IDs
 * @param bool $exclude_displayed Whether to exclude already displayed coupons
 * @param string $section Section identifier for tracking
 * @return array Array of coupon objects
 */
function get_coupons_by_ids($ids = array(), $exclude_displayed = true, $section = 'custom') {
    if (empty($ids)) {
        return array();
    }

    $tracker = AG_Coupon_Tracker::get_instance();

    // Filter out displayed coupons if requested
    if ($exclude_displayed) {
        $displayed_coupons = $tracker->get_displayed_coupons();
        $ids = array_diff($ids, $displayed_coupons);
    }

    if (empty($ids)) {
        return array();
    }

    return get_posts(array(
        'post_type' => 'coupon',
        'post__in' => $ids,
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
}

/**
 * Render enhanced coupon card with tracking and deduplication
 *
 * @param WP_Post|object $coupon Coupon object or post
 * @param string $style Card style: 'featured', 'default', 'minimal', 'scratch'
 * @param array $args Additional arguments
 */
function render_enhanced_coupon_card($coupon, $style = 'default', $args = array()) {
    if (!$coupon) return;

    $tracker = AG_Coupon_Tracker::get_instance();

    // Check if coupon is already displayed (optional strict mode)
    $strict_mode = isset($args['strict_deduplication']) ? $args['strict_deduplication'] : false;
    if ($strict_mode && $tracker->is_coupon_displayed($coupon->ID)) {
        return; // Skip rendering if already displayed
    }

    // Set up coupon context
    wpcoupon_setup_coupon($coupon);

    // Default arguments
    $defaults = array(
        'show_store_logo' => true,
        'show_badges' => true,
        'show_description' => true,
        'description_length' => 15,
        'section' => 'default',
        'track_display' => true,
        'strict_deduplication' => false,
        'scratch_effect' => false,
        'hover_effects' => true
    );

    $args = wp_parse_args($args, $defaults);

    // Track this coupon as displayed
    if ($args['track_display']) {
        $tracker->track_coupon($coupon->ID, $args['section']);
    }

    // Set query vars for the template
    set_query_var('coupon_card_style', $style);
    set_query_var('coupon_card_args', $args);

    // Load the appropriate template
    get_template_part('template-parts/components/enhanced-coupon-card');

    // Clean up
    set_query_var('coupon_card_style', null);
    set_query_var('coupon_card_args', null);
}

/**
 * Render coupons grid with deduplication tracking
 *
 * @param array $coupons Array of coupon objects
 * @param string $style Grid style: 'featured', 'default', 'minimal', 'scratch'
 * @param array $grid_args Grid configuration
 */
function render_coupons_grid($coupons, $style = 'default', $grid_args = array()) {
    if (empty($coupons)) return;

    // Default grid configuration
    $defaults = array(
        'columns' => 4,
        'columns_tablet' => 2,
        'columns_mobile' => 1,
        'gap' => 6,
        'container_class' => 'coupons-grid-container',
        'section' => 'grid',
        'track_coupons' => true,
        'show_load_more' => false,
        'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon')
    );

    $grid_args = wp_parse_args($grid_args, $defaults);

    $grid_classes = sprintf(
        'grid grid-cols-%d md:grid-cols-%d lg:grid-cols-%d gap-%d',
        $grid_args['columns_mobile'],
        $grid_args['columns_tablet'],
        $grid_args['columns'],
        $grid_args['gap']
    );
    ?>

    <div class="<?php echo esc_attr($grid_args['container_class']); ?>">
        <div class="<?php echo esc_attr($grid_classes); ?>">
            <?php foreach ($coupons as $coupon) : ?>
                <div class="coupon-grid-item">
                    <?php
                    render_enhanced_coupon_card($coupon, $style, array(
                        'section' => $grid_args['section'],
                        'track_display' => $grid_args['track_coupons']
                    ));
                    ?>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if ($grid_args['show_load_more']) : ?>
            <div class="text-center mt-8">
                <button class="ag-load-more-coupons bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                    <?php echo esc_html($grid_args['load_more_text']); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * Enhanced coupons shortcode with deduplication
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 */
function enhanced_coupons_shortcode($atts) {
    $atts = shortcode_atts(array(
        'type' => 'latest', // featured, latest, ids
        'number' => 8,
        'style' => 'default', // featured, default, minimal, scratch
        'columns' => 4,
        'gap' => 6,
        'ids' => '',
        'exclude' => '',
        'show_load_more' => 'false',
        'section' => 'shortcode'
    ), $atts, 'ag_coupons');

    ob_start();

    // Get coupons based on type with deduplication
    $coupons = array();
    $section = 'shortcode-' . $atts['type'];

    switch ($atts['type']) {
        case 'featured':
            $coupons = get_featured_coupons(
                intval($atts['number']),
                true, // exclude displayed
                $section
            );
            break;

        case 'latest':
            $exclude_ids = !empty($atts['exclude']) ?
                array_map('intval', explode(',', $atts['exclude'])) : array();
            $coupons = get_all_coupons(
                intval($atts['number']),
                $exclude_ids,
                true, // exclude displayed
                $section
            );
            break;

        case 'ids':
            if (!empty($atts['ids'])) {
                $coupon_ids = array_map('intval', explode(',', $atts['ids']));
                $coupons = get_coupons_by_ids(
                    $coupon_ids,
                    true, // exclude displayed
                    $section
                );
            }
            break;
    }

    // Render coupons grid
    if (!empty($coupons)) {
        render_coupons_grid($coupons, $atts['style'], array(
            'columns' => intval($atts['columns']),
            'gap' => intval($atts['gap']),
            'section' => $section,
            'track_coupons' => true,
            'show_load_more' => $atts['show_load_more'] === 'true'
        ));
    } else {
        echo '<p class="text-center text-gray-500 py-8">' . esc_html__('لا توجد كوبونات متاحة حالياً', 'wp-coupon') . '</p>';
    }

    return ob_get_clean();
}
add_shortcode('ag_coupons', 'enhanced_coupons_shortcode');

/**
 * Utility Functions for Coupon Deduplication Management
 */

/**
 * Get coupon tracker instance
 */
function ag_get_coupon_tracker() {
    return AG_Coupon_Tracker::get_instance();
}

/**
 * Reset coupon tracking (useful for testing)
 */
function ag_reset_coupon_tracking() {
    AG_Coupon_Tracker::get_instance()->reset();
}

/**
 * Get coupon tracking statistics
 */
function ag_get_coupon_stats() {
    return AG_Coupon_Tracker::get_instance()->get_stats();
}

/**
 * Check if coupon is already displayed
 */
function ag_is_coupon_displayed($coupon_id) {
    return AG_Coupon_Tracker::get_instance()->is_coupon_displayed($coupon_id);
}

/**
 * Debug function to display coupon tracking info (for development)
 */
function ag_debug_coupon_tracking() {
    if (!WP_DEBUG) return;

    $tracker = AG_Coupon_Tracker::get_instance();
    $stats = $tracker->get_stats();

    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">';
    echo '<h4>Coupon Tracking Debug Info:</h4>';
    echo '<p><strong>Total Displayed:</strong> ' . $stats['total_displayed'] . '</p>';
    echo '<p><strong>Unique Coupons:</strong> ' . $stats['unique_coupons'] . '</p>';
    echo '<p><strong>Duplicates Prevented:</strong> ' . $stats['duplicates_prevented'] . '</p>';
    echo '<p><strong>Sections:</strong> ' . implode(', ', $stats['sections']) . '</p>';
    echo '<p><strong>Displayed Coupon IDs:</strong> ' . implode(', ', $tracker->get_displayed_coupons()) . '</p>';
    echo '</div>';
}
