/**
 * Ag-Coupon Theme - Brand New Clean JavaScript Implementation
 * Built from scratch for proper functionality
 */

(function($) {
    'use strict';

    // Global variables - Wait for ST object to be available
    window.AgCoupon = {
        ajax_url: null,
        nonce: null,
        debug: true, // Enable debugging for development
        initialized: false
    };

    // Initialize when ST object is available
    function initializeAgCoupon() {
        console.log('🔧 Attempting to initialize AgCoupon...');
        console.log('🔧 ST object available:', typeof ST !== 'undefined');
        if (typeof ST !== 'undefined') {
            console.log('🔧 ST.ajax_url:', ST.ajax_url);
            console.log('🔧 ST._wpnonce:', ST._wpnonce);
        }

        if (typeof ST !== 'undefined' && ST.ajax_url && ST._wpnonce) {
            window.AgCoupon.ajax_url = ST.ajax_url;
            window.AgCoupon.nonce = ST._wpnonce;
            window.AgCoupon.initialized = true;
            console.log('✅ AgCoupon initialized successfully!');
            log('✅ AgCoupon initialized with ST object');
            return true;
        }
        console.error('❌ AgCoupon initialization failed - ST object not ready');
        return false;
    }

    /**
     * Debug logging
     */
    function log(message, data = null) {
        if (window.AgCoupon.debug) {
            console.log('🎯 AgCoupon:', message, data || '');
        }
    }

    /**
     * Cookie utilities
     */
    function setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for(let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    /**
     * AJAX helper function
     */
    function ajaxRequest(action, data, callback) {
        if (!window.AgCoupon.initialized) {
            console.error('❌ AJAX Request failed: AgCoupon not initialized');
            log('❌ AJAX Request failed: AgCoupon not initialized');
            return;
        }

        const requestData = {
            action: 'wpcoupon_coupon_ajax',
            st_doing: action,
            _wpnonce: window.AgCoupon.nonce,
            ...data
        };

        log(`🚀 AJAX Request: ${action}`, requestData);
        log(`📡 AJAX URL: ${window.AgCoupon.ajax_url}`);
        log(`🔑 Key parameters:`, {
            action: requestData.action,
            st_doing: requestData.st_doing,
            next_page: requestData.next_page,
            args: requestData.args
        });

        $.ajax({
            url: window.AgCoupon.ajax_url,
            type: 'POST',
            data: requestData,
            dataType: 'json',
            beforeSend: function() {
                log(`⏳ AJAX ${action}: Request sent...`);
            },
            success: function(response) {
                log(`✅ AJAX Success: ${action}`, response);
                log(`📦 Response type: ${typeof response}, Success: ${response.success}`);

                // Detailed response analysis
                if (response.data) {
                    log(`📄 Response data:`, response.data);
                    log(`📄 Content exists: ${!!response.data.content}`);
                    log(`📄 Content length: ${response.data.content ? response.data.content.length : 'No content'}`);
                    log(`📄 Next page: ${response.data.next_page}`);
                    log(`📄 Max pages: ${response.data.max_pages}`);
                } else {
                    log(`❌ No response.data found`);
                }

                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                log(`❌ AJAX Error: ${action}`, {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });
                console.error('AJAX Error Details:', xhr);
            }
        });
    }

    /**
     * Coupon Click Tracking
     */
    function trackCouponUsage(couponId) {
        if (!couponId) return;

        log('Tracking coupon usage', couponId);

        ajaxRequest('tracking_coupon', {
            coupon_id: couponId
        }, function(response) {
            log('Coupon tracked successfully', response);
        });
    }

    /**
     * Coupon Voting System
     */
    function initVotingSystem() {
        log('Initializing voting system');

        // Handle vote clicks
        $(document).on('click', '.coupon-vote', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $wrapper = $btn.closest('.coupon-vote-wrapper');
            const couponId = $btn.data('coupon-id');
            const voteType = $btn.data('vote-type');

            log('Vote clicked', {couponId, voteType});

            // Check if already voted
            if ($wrapper.hasClass('voted')) {
                log('Already voted, ignoring');
                return false;
            }

            // Update UI immediately
            $btn.addClass('active');
            $wrapper.addClass('voted');

            // Store vote in cookie
            setCookie(`c_vote_id_${couponId}`, voteType, 30);

            // Send AJAX request
            ajaxRequest('vote_coupon', {
                coupon_id: couponId,
                vote_type: voteType
            }, function(response) {
                log('Vote successful', response);
            });
        });

        // Initialize existing votes from cookies
        $('.coupon-vote').each(function() {
            const $btn = $(this);
            const couponId = $btn.data('coupon-id');
            const voteType = $btn.data('vote-type');
            const savedVote = getCookie(`c_vote_id_${couponId}`);

            if (savedVote === voteType) {
                $btn.addClass('active');
                $btn.closest('.coupon-vote-wrapper').addClass('voted');
            }
        });

        log('Voting system initialized');
    }

    /**
     * Reveal Content System
     */
    function initRevealContent() {
        log('Initializing reveal content system');

        $(document).on('click', '[data-reveal]', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const revealClass = $btn.data('reveal');
            const $card = $btn.closest('.store-listing-item');
            const $revealContent = $card.find(`.${revealClass}`);

            log('Reveal clicked', revealClass);

            // Toggle reveal content
            if ($revealContent.hasClass('active')) {
                $revealContent.removeClass('active').hide();
                $btn.removeClass('active');
            } else {
                // Hide other reveal content in this card
                $card.find('.reveal-content').removeClass('active').hide();
                $card.find('[data-reveal]').removeClass('active');

                // Show this reveal content
                $revealContent.addClass('active').show();
                $btn.addClass('active');

                // Load comments if needed
                if (revealClass === 'reveal-comments' && !$revealContent.hasClass('comments-loaded')) {
                    loadCouponComments($revealContent);
                }
            }
        });

        // Close reveal content
        $(document).on('click', '.reveal-content .close', function(e) {
            e.preventDefault();

            const $revealContent = $(this).closest('.reveal-content');
            const $card = $revealContent.closest('.store-listing-item');

            $revealContent.removeClass('active').hide();
            $card.find('[data-reveal]').removeClass('active');
        });

        log('Reveal content system initialized');
    }

    /**
     * Load coupon comments
     */
    function loadCouponComments($revealContent) {
        const couponId = $revealContent.data('coupon-id');
        const $commentsArea = $revealContent.find(`.comments-coupon-${couponId}`);

        log('Loading comments for coupon', couponId);

        $revealContent.addClass('comments-loaded');

        ajaxRequest('get_coupon_comments', {
            coupon_id: couponId
        }, function(response) {
            if (response.data) {
                $commentsArea.html(response.data);
                log('Comments loaded successfully');
            }
        });
    }

    /**
     * Coupon Click Handler - Separate handlers for title and button
     */
    function initCouponClickHandler() {
        log('Initializing coupon click handler');

        // Handle coupon button clicks
        $(document).on('click', '.coupon-button', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const couponId = $btn.data('coupon-id');
            const affUrl = $btn.data('aff-url');
            const couponUrl = $btn.attr('href');
            const couponCode = $btn.data('code');

            log('Coupon BUTTON clicked', {couponId, affUrl, couponUrl, couponCode});

            // Track usage
            if (couponId) {
                trackCouponUsage(couponId);
            }

            // Copy code to clipboard if available
            if (couponCode) {
                copyToClipboard(couponCode);
            }

            // Open URLs
            if (affUrl) {
                window.open(affUrl, '_self');
            }
            if (couponUrl) {
                window.open(couponUrl, '_blank');
            }
        });

        // Handle coupon title link clicks (distinct behavior)
        $(document).on('click', '.coupon-title-link', function(e) {
            e.preventDefault();

            const $link = $(this);
            const couponId = $link.data('coupon-id');
            const affUrl = $link.data('aff-url');
            const couponUrl = $link.attr('href');
            const couponCode = $link.data('code');

            log('Coupon TITLE clicked', {couponId, affUrl, couponUrl, couponCode});

            // Track usage
            if (couponId) {
                trackCouponUsage(couponId);
            }

            // Copy code to clipboard if available
            if (couponCode) {
                copyToClipboard(couponCode);
            }

            // Open URLs (same behavior as button for now, can be customized)
            if (affUrl) {
                window.open(affUrl, '_self');
            }
            if (couponUrl) {
                window.open(couponUrl, '_blank');
            }
        });

        log('Coupon click handler initialized');
    }

    /**
     * Copy to clipboard
     */
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                log('Text copied to clipboard', text);
                showCopyFeedback();
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            log('Text copied to clipboard (fallback)', text);
            showCopyFeedback();
        }
    }

    /**
     * Show copy feedback
     */
    function showCopyFeedback() {
        // You can customize this feedback
        log('Copy feedback shown');
    }

    /**
     * Enhanced Load More Functionality - Brand New Implementation
     */
    function initLoadMore() {
        log('Initializing enhanced load more functionality');

        // Handle load more button clicks - Updated selectors for new design
        $(document).on('click', '.load-more-btn, .load-more .button, .wpc-load-more, .button.wpc-load-more, .ag-load-more-coupons', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $loadMoreWrapper = $btn.closest('.load-more-container');
            const $container = $('#latest-coupons-grid'); // Target the grid directly, not its parent

            console.log('🖱️ Load more button clicked!');
            console.log('🔍 Button details:', {
                button: this,
                classes: $btn.attr('class'),
                data: $btn.data(),
                container: $container[0],
                containerLength: $container.length
            });
            log('🖱️ Load more button clicked!');

            // Prevent multiple clicks
            if ($btn.hasClass('loading') || $btn.prop('disabled')) {
                console.log('Load more already in progress, ignoring click');
                log('Load more already in progress, ignoring click');
                return false;
            }

            log('Load more clicked', {
                button: $btn[0],
                container: $container[0],
                wrapper: $loadMoreWrapper[0],
                containerLength: $container.length,
                wrapperLength: $loadMoreWrapper.length
            });

            // Start loading state
            startLoadingState($btn);

            // Prepare AJAX data
            const ajaxData = buildLoadMoreData($btn);
            const action = ajaxData.doing || determineLoadMoreAction(ajaxData);

            log('Load more AJAX data prepared', {action, ajaxData});

            // Execute AJAX request with correct st_doing parameter
            // The ajaxRequest function expects the action as st_doing, so we pass the action correctly
            console.log('🚀 Executing AJAX request...', {action, ajaxData});
            log('🚀 Executing AJAX request...', {action, ajaxData});

            ajaxRequest(action, {
                ...ajaxData,
                st_doing: action  // Ensure st_doing is set correctly
            }, function(response) {
                console.log('📥 AJAX response received:', response);
                log('📥 AJAX response received:', response);
                handleLoadMoreResponse($btn, $loadMoreWrapper, $container, response);
            });
        });

        log('Enhanced load more functionality initialized');
    }

    /**
     * Start loading state for load more button
     */
    function startLoadingState($btn) {
        $btn.addClass('loading').prop('disabled', true);

        // Show loading spinner and hide button text
        $btn.find('.loading-spinner').removeClass('hidden');
        $btn.find('.btn-text').addClass('opacity-50');
        $btn.find('.btn-icon').addClass('animate-bounce');

        // Show progress indicator
        $btn.closest('.load-more-container').find('.loading-progress').removeClass('hidden');

        log('✨ Enhanced loading state started');
    }

    /**
     * End loading state for load more button
     */
    function endLoadingState($btn) {
        $btn.removeClass('loading').prop('disabled', false);

        // Hide loading spinner and restore button text
        $btn.find('.loading-spinner').addClass('hidden');
        $btn.find('.btn-text').removeClass('opacity-50');
        $btn.find('.btn-icon').removeClass('animate-bounce');

        // Hide progress indicator
        $btn.closest('.load-more-container').find('.loading-progress').addClass('hidden');

        log('✨ Enhanced loading state ended');
    }

    /**
     * Build AJAX data for load more request
     */
    function buildLoadMoreData($btn) {
        const nextPage = $btn.data('next-page') || $btn.attr('data-next-page') || 2;
        const args = $btn.data('args') || {};

        // Collect currently displayed coupon IDs to prevent duplicates
        const displayedCouponIds = [];
        $('#latest-coupons-grid [data-coupon-id]').each(function() {
            const couponId = $(this).data('coupon-id');
            if (couponId) {
                displayedCouponIds.push(couponId);
            }
        });

        // Build args object as expected by wpcoupon_ajax_coupons
        const ajaxArgs = {
            layout: args.layout || '',
            posts_per_page: $btn.data('per-page') || args.posts_per_page || '',
            num_words: args.num_words || '',
            hide_expired: args.hide_expired || '',
            exclude_ids: displayedCouponIds // Pass displayed coupon IDs to exclude
        };

        log('🔍 Collected displayed coupon IDs:', displayedCouponIds);

        return {
            current_link: $btn.data('link') || $btn.attr('href'),
            cat_id: $btn.data('cat-id'),
            store_id: $btn.data('store-id'),
            next_page: nextPage,  // This is the key parameter
            args: ajaxArgs,       // Args object as expected by the function
            filter_type: $btn.data('filter-type'),
            search_query: $btn.data('search-query'),
            doing: $btn.data('doing') || 'load_coupons',
            displayed_coupon_ids: displayedCouponIds // Also pass as separate parameter
        };
    }

    /**
     * Determine the correct AJAX action based on data
     */
    function determineLoadMoreAction(ajaxData) {
        if (ajaxData.cat_id) {
            return 'load_category_coupons';
        } else if (ajaxData.store_id) {
            return 'load_store_coupons';
        } else if (ajaxData.search_query) {
            return 'load_search_coupons';
        } else {
            return 'load_coupons';
        }
    }

    /**
     * Handle load more AJAX response
     */
    function handleLoadMoreResponse($btn, $loadMoreWrapper, $container, response) {
        endLoadingState($btn);

        log('📥 Processing AJAX response', {
            success: response.success,
            hasData: !!response.data,
            hasContent: !!(response.data && response.data.content),
            contentLength: response.data && response.data.content ? response.data.content.length : 0
        });

        if (response.success && response.data && response.data.content) {
            const $newContent = $(response.data.content);

            // Find the target container for new content
            let $targetContainer = $('#latest-coupons-grid');

            // Fallback to other possible containers
            if ($targetContainer.length === 0) {
                $targetContainer = $container.find('.st-list-coupons, .coupon-list, .coupons-grid');
            }

            // Final fallback to container itself
            if ($targetContainer.length === 0) {
                $targetContainer = $container;
            }

            log('🎯 Target container found', {
                targetId: $targetContainer.attr('id'),
                targetClass: $targetContainer.attr('class'),
                targetLength: $targetContainer.length,
                newContentLength: $newContent.length
            });

            // Debug: Log what we're about to append
            log('🎯 About to append content', {
                newContentHtml: $newContent.length > 0 && $newContent[0] && $newContent[0].outerHTML ?
                    $newContent[0].outerHTML.substring(0, 200) + '...' : 'No content or invalid element',
                targetContainerHtml: $targetContainer.length > 0 && $targetContainer[0] && $targetContainer[0].outerHTML ?
                    $targetContainer[0].outerHTML.substring(0, 100) + '...' : 'No container or invalid element'
            });

            // Append new content with animation
            $newContent.hide().appendTo($targetContainer).fadeIn(300);

            // Debug: Verify content was appended
            log('🎯 Content appended, new container children count:', $targetContainer.children().length);

            // Update pagination data
            updatePaginationData($btn, response.data);

            // Remove load more if no more pages
            if (shouldRemoveLoadMore(response.data)) {
                $loadMoreWrapper.fadeOut(300, function() {
                    $(this).remove();
                });
                log('Load more removed - no more content');
            }

            // Trigger custom event for other scripts
            $(document).trigger('ag-coupon:content-loaded', [$newContent]);

            log('✅ Load more content successfully added', {
                newItems: $newContent.length,
                nextPage: response.data.next_page,
                hasMore: !shouldRemoveLoadMore(response.data)
            });

        } else {
            log('❌ Load more failed or no content', {
                success: response.success,
                data: response.data,
                error: response.error || 'Unknown error'
            });
            showLoadMoreError($btn);
        }
    }

    /**
     * Update pagination data after successful load
     */
    function updatePaginationData($btn, responseData) {
        if (responseData.next_page !== undefined) {
            $btn.attr('data-next-page', responseData.next_page);
            $btn.data('next-page', responseData.next_page);
        }

        if (responseData.current_page !== undefined) {
            $btn.attr('data-current-page', responseData.current_page);
            $btn.data('current-page', responseData.current_page);
        }
    }

    /**
     * Check if load more should be removed
     */
    function shouldRemoveLoadMore(responseData) {
        return responseData.next_page <= 0 ||
               responseData.has_more === false ||
               !responseData.content ||
               responseData.content.trim() === '';
    }

    /**
     * Show load more error message
     */
    function showLoadMoreError($btn) {
        const originalText = $btn.data('original-text');
        $btn.html('❌ خطأ في التحميل - حاول مرة أخرى');

        setTimeout(function() {
            if (originalText) {
                $btn.html(originalText);
            }
        }, 3000);
    }

    /**
     * Read More System for Store Descriptions
     */
    function initReadMoreSystem() {
        log('Initializing read more system');

        // Handle read more button clicks
        $(document).on('click', '.read-more-btn, #expandbtn', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $contentWrapper = $btn.closest('.store-content-wrapper');
            const $content = $contentWrapper.find('.store-content, #store-content');

            log('Read more clicked', {
                button: $btn[0],
                contentWrapper: $contentWrapper[0],
                content: $content[0]
            });

            // Toggle expanded state
            if ($content.hasClass('expanded')) {
                // Collapse content
                $content.removeClass('expanded');
                $btn.html('إقرأ المزيد');

                // Add smooth scroll to top of content
                $('html, body').animate({
                    scrollTop: $content.offset().top - 100
                }, 300);

                log('Content collapsed');
            } else {
                // Expand content
                $content.addClass('expanded');
                $btn.html('إقرأ أقل');

                log('Content expanded');
            }
        });

        log('Read more system initialized');
    }

    /**
     * Debug function to test load more manually
     */
    window.testLoadMore = function() {
        const $btn = $('.load-more-btn, .wpc-load-more, .button.wpc-load-more').first();
        if ($btn.length) {
            log('=== TESTING LOAD MORE ===');
            log('Button found:', $btn[0]);
            log('Button classes:', $btn.attr('class'));
            log('Button data:', {
                doing: $btn.data('doing'),
                nextPage: $btn.data('next-page'),
                loadingText: $btn.data('loading-text'),
                allData: $btn.data()
            });

            // Test AJAX directly
            const ajaxData = buildLoadMoreData($btn);
            log('AJAX data that will be sent:', ajaxData);

            // Test container detection
            const $container = $('#latest-coupons-grid');
            log('Container detection:', {
                container: $container[0],
                containerLength: $container.length,
                targetGrid: $container[0],
                targetGridLength: $container.length
            });

            $btn.trigger('click');
        } else {
            log('❌ No load more button found');
            log('Available buttons:', $('.load-more-btn, .wpc-load-more, .button').length);
        }
    };

    // Debug functions available in development mode only
    if (window.AgCoupon && window.AgCoupon.debug) {
        /**
         * Test AJAX endpoint directly
         */
        window.testAjaxDirect = function() {
            const testData = {
                action: 'wpcoupon_coupon_ajax',
                st_doing: 'load_coupons',
                next_page: 2,
                _wpnonce: window.AgCoupon.nonce,
                args: {
                    layout: '',
                    posts_per_page: '',
                    num_words: '',
                    hide_expired: ''
                }
            };

            log('🧪 Testing AJAX directly with data:', testData);

            $.ajax({
                url: window.AgCoupon.ajax_url,
                type: 'POST',
                data: testData,
                dataType: 'json',
                success: function(response) {
                    log('🧪 Direct AJAX Success:', response);
                    if (response.success && response.data && response.data.content) {
                        log('✅ AJAX returned content:', response.data.content.length + ' characters');
                        log('📄 Content preview:', response.data.content.substring(0, 200) + '...');
                    } else {
                        log('❌ AJAX response has no content');
                    }
                },
                error: function(xhr, status, error) {
                    log('🧪 Direct AJAX Error:', {xhr, status, error});
                    log('Response text:', xhr.responseText);
                }
            });
        };
    }

    // Always available debug functions
    window.debugLoadMore = function() {
        log('=== LOAD MORE DEBUG ===');

        // Check if AgCoupon is initialized
        log('AgCoupon initialized:', !!window.AgCoupon);
        if (window.AgCoupon) {
            log('AJAX URL:', window.AgCoupon.ajax_url);
            log('Nonce:', window.AgCoupon.nonce);
        }

        // Check for load more buttons
        const $buttons = $('.load-more-btn, .ag-load-more-coupons');
        log('Load more buttons found:', $buttons.length);

        $buttons.each(function(i) {
            const $btn = $(this);
            log(`Button ${i+1}:`, {
                element: this,
                classes: $btn.attr('class'),
                data: $btn.data(),
                text: $btn.text().trim()
            });
        });

        // Check for target containers
        const $container = $('#latest-coupons-grid');
        log('Target container (#latest-coupons-grid):', {
            found: $container.length > 0,
            element: $container[0],
            children: $container.children().length
        });

        // Test AJAX if possible
        if (window.AgCoupon && $buttons.length > 0) {
            log('Testing AJAX call...');
            window.testAjaxDirect();
        }
    };

    /**
     * Initialize everything when document is ready
     */
    $(document).ready(function() {
        log('🚀 Ag-Coupon New JavaScript System Starting...');

        // Initialize AgCoupon with ST object (should be available since we depend on wpcoupon_global)
        if (initializeAgCoupon()) {
            // Initialize all systems
            initVotingSystem();
            initRevealContent();
            initCouponClickHandler();
            initLoadMore();
            initReadMoreSystem();
            log('✅ All systems initialized successfully!');

            // Debug: Check for load more buttons
            const $loadMoreBtns = $('.load-more-btn, .wpc-load-more, .button.wpc-load-more, .load-more .button');
            log('🔍 Load more buttons found:', $loadMoreBtns.length);
            $loadMoreBtns.each(function(i) {
                log(`Button ${i+1}:`, this, $(this).data());
            });

        } else {
            // Fallback: wait for ST object if not immediately available
            log('⏳ ST object not ready, waiting...');
            setTimeout(function() {
                if (initializeAgCoupon()) {
                    initVotingSystem();
                    initRevealContent();
                    initCouponClickHandler();
                    initLoadMore();
                    initReadMoreSystem();
                    log('✅ All systems initialized successfully (delayed)!');
                } else {
                    log('❌ Failed to initialize: ST object not found');
                }
            }, 500);
        }
    });

})(jQuery);
