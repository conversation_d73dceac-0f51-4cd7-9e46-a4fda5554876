/**
 * Ag-Coupon Theme - Brand New Clean JavaScript Implementation
 * Built from scratch for proper functionality
 */

(function($) {
    'use strict';

    // Global variables - Wait for ST object to be available
    window.AgCoupon = {
        ajax_url: null,
        nonce: null,
        initialized: false
    };

    // Initialize when ST object is available
    function initializeAgCoupon() {
        if (typeof ST !== 'undefined' && ST.ajax_url && ST._wpnonce) {
            window.AgCoupon.ajax_url = ST.ajax_url;
            window.AgCoupon.nonce = ST._wpnonce;
            window.AgCoupon.initialized = true;
            return true;
        }
        return false;
    }

    /**
     * Cookie utilities
     */
    function setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for(let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    /**
     * AJAX helper function
     */
    function ajaxRequest(action, data, callback) {
        if (!window.AgCoupon.initialized) {
            return;
        }

        const requestData = {
            action: 'wpcoupon_coupon_ajax',
            st_doing: action,
            _wpnonce: window.AgCoupon.nonce,
            ...data
        };

        $.ajax({
            url: window.AgCoupon.ajax_url,
            type: 'POST',
            data: requestData,
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                // Handle error silently
            }
        });
    }

    /**
     * Coupon Click Tracking
     */
    function trackCouponUsage(couponId) {
        if (!couponId) return;ajaxRequest('tracking_coupon', {
            coupon_id: couponId
        }, function(response) {});
    }

    /**
     * Coupon Voting System
     */
    function initVotingSystem() {// Handle vote clicks
        $(document).on('click', '.coupon-vote', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $wrapper = $btn.closest('.coupon-vote-wrapper');
            const couponId = $btn.data('coupon-id');
            const voteType = $btn.data('vote-type');// Check if already voted
            if ($wrapper.hasClass('voted')) {return false;
            }

            // Update UI immediately
            $btn.addClass('active');
            $wrapper.addClass('voted');

            // Store vote in cookie
            setCookie(`c_vote_id_${couponId}`, voteType, 30);

            // Send AJAX request
            ajaxRequest('vote_coupon', {
                coupon_id: couponId,
                vote_type: voteType
            }, function(response) {});
        });

        // Initialize existing votes from cookies
        $('.coupon-vote').each(function() {
            const $btn = $(this);
            const couponId = $btn.data('coupon-id');
            const voteType = $btn.data('vote-type');
            const savedVote = getCookie(`c_vote_id_${couponId}`);

            if (savedVote === voteType) {
                $btn.addClass('active');
                $btn.closest('.coupon-vote-wrapper').addClass('voted');
            }
        });}

    /**
     * Reveal Content System
     */
    function initRevealContent() {$(document).on('click', '[data-reveal]', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const revealClass = $btn.data('reveal');
            const $card = $btn.closest('.store-listing-item');
            const $revealContent = $card.find(`.${revealClass}`);// Toggle reveal content
            if ($revealContent.hasClass('active')) {
                $revealContent.removeClass('active').hide();
                $btn.removeClass('active');
            } else {
                // Hide other reveal content in this card
                $card.find('.reveal-content').removeClass('active').hide();
                $card.find('[data-reveal]').removeClass('active');

                // Show this reveal content
                $revealContent.addClass('active').show();
                $btn.addClass('active');

                // Load comments if needed
                if (revealClass === 'reveal-comments' && !$revealContent.hasClass('comments-loaded')) {
                    loadCouponComments($revealContent);
                }
            }
        });

        // Close reveal content
        $(document).on('click', '.reveal-content .close', function(e) {
            e.preventDefault();

            const $revealContent = $(this).closest('.reveal-content');
            const $card = $revealContent.closest('.store-listing-item');

            $revealContent.removeClass('active').hide();
            $card.find('[data-reveal]').removeClass('active');
        });}

    /**
     * Load coupon comments
     */
    function loadCouponComments($revealContent) {
        const couponId = $revealContent.data('coupon-id');
        const $commentsArea = $revealContent.find(`.comments-coupon-${couponId}`);$revealContent.addClass('comments-loaded');

        ajaxRequest('get_coupon_comments', {
            coupon_id: couponId
        }, function(response) {
            if (response.data) {
                $commentsArea.html(response.data);}
        });
    }

    /**
     * Coupon Click Handler - Separate handlers for title and button
     */
    function initCouponClickHandler() {// Handle coupon button clicks
        $(document).on('click', '.coupon-button', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const couponId = $btn.data('coupon-id');
            const affUrl = $btn.data('aff-url');
            const couponUrl = $btn.attr('href');
            const couponCode = $btn.data('code');// Track usage
            if (couponId) {
                trackCouponUsage(couponId);
            }

            // Copy code to clipboard if available
            if (couponCode) {
                copyToClipboard(couponCode);
            }

            // Open URLs
            if (affUrl) {
                window.open(affUrl, '_self');
            }
            if (couponUrl) {
                window.open(couponUrl, '_blank');
            }
        });

        // Handle coupon title link clicks (distinct behavior)
        $(document).on('click', '.coupon-title-link', function(e) {
            e.preventDefault();

            const $link = $(this);
            const couponId = $link.data('coupon-id');
            const affUrl = $link.data('aff-url');
            const couponUrl = $link.attr('href');
            const couponCode = $link.data('code');// Track usage
            if (couponId) {
                trackCouponUsage(couponId);
            }

            // Copy code to clipboard if available
            if (couponCode) {
                copyToClipboard(couponCode);
            }

            // Open URLs (same behavior as button for now, can be customized)
            if (affUrl) {
                window.open(affUrl, '_self');
            }
            if (couponUrl) {
                window.open(couponUrl, '_blank');
            }
        });}

    /**
     * Copy to clipboard
     */
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {showCopyFeedback();
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);showCopyFeedback();
        }
    }

    /**
     * Show copy feedback
     */
    function showCopyFeedback() {
        // You can customize this feedback
    }

    /**
     * Enhanced Load More Functionality - Brand New Implementation
     */
    function initLoadMore() {
        // Handle load more button clicks - Updated selectors for new design
        $(document).on('click', '.load-more-btn, .load-more .button, .wpc-load-more, .button.wpc-load-more, .ag-load-more-coupons', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $loadMoreWrapper = $btn.closest('.load-more-container');
            const $container = $('#home-latest-coupons-grid-grid'); // Target the correct latest coupons grid

            // Prevent multiple clicks
            if ($btn.hasClass('loading') || $btn.prop('disabled')) {
                return false;
            }

            // Start loading state
            startLoadingState($btn);

            // Prepare AJAX data
            const ajaxData = buildLoadMoreData($btn);
            const action = ajaxData.doing || determineLoadMoreAction(ajaxData);

            // Execute AJAX request with correct st_doing parameter
            // The ajaxRequest function expects the action as st_doing, so we pass the action correctly
            ajaxRequest(action, {
                ...ajaxData,
                st_doing: action  // Ensure st_doing is set correctly
            }, function(response) {
                handleLoadMoreResponse($btn, $loadMoreWrapper, $container, response);
            });
        });
    }

    /**
     * Start loading state for load more button
     */
    function startLoadingState($btn) {
        $btn.addClass('loading').prop('disabled', true);

        // Show loading spinner and hide button text
        $btn.find('.loading-spinner').removeClass('hidden');
        $btn.find('.btn-text').addClass('opacity-50');
        $btn.find('.btn-icon').addClass('animate-bounce');

        // Show progress indicator
        $btn.closest('.load-more-container').find('.loading-progress').removeClass('hidden');
    }

    /**
     * End loading state for load more button
     */
    function endLoadingState($btn) {
        $btn.removeClass('loading').prop('disabled', false);

        // Hide loading spinner and restore button text
        $btn.find('.loading-spinner').addClass('hidden');
        $btn.find('.btn-text').removeClass('opacity-50');
        $btn.find('.btn-icon').removeClass('animate-bounce');

        // Hide progress indicator
        $btn.closest('.load-more-container').find('.loading-progress').addClass('hidden');
    }

    /**
     * Build AJAX data for load more request
     */
    function buildLoadMoreData($btn) {
        const nextPage = $btn.data('next-page') || $btn.attr('data-next-page') || 2;
        const args = $btn.data('args') || {};

        // Collect currently displayed coupon IDs to prevent duplicates
        const displayedCouponIds = [];
        const uniqueIds = new Set();

        // Only collect from the main article elements to avoid duplicates
        $('#home-latest-coupons-grid-grid article[data-coupon-id]').each(function() {
            const couponId = $(this).data('coupon-id');
            if (couponId && !uniqueIds.has(couponId)) {
                uniqueIds.add(couponId);
                displayedCouponIds.push(couponId);
            }
        });

        // Build args object as expected by wpcoupon_ajax_coupons
        const ajaxArgs = {
            layout: args.layout || '',
            posts_per_page: $btn.data('per-page') || args.posts_per_page || '',
            num_words: args.num_words || '',
            hide_expired: args.hide_expired || '',
            exclude_ids: displayedCouponIds // Pass displayed coupon IDs to exclude
        };

        return {
            current_link: $btn.data('link') || $btn.attr('href'),
            cat_id: $btn.data('cat-id'),
            store_id: $btn.data('store-id'),
            next_page: nextPage,  // This is the key parameter
            args: ajaxArgs,       // Args object as expected by the function
            filter_type: $btn.data('filter-type'),
            search_query: $btn.data('search-query'),
            doing: $btn.data('doing') || 'load_coupons',
            displayed_coupon_ids: displayedCouponIds // Also pass as separate parameter
        };
    }

    /**
     * Determine the correct AJAX action based on data
     */
    function determineLoadMoreAction(ajaxData) {
        if (ajaxData.cat_id) {
            return 'load_category_coupons';
        } else if (ajaxData.store_id) {
            return 'load_store_coupons';
        } else if (ajaxData.search_query) {
            return 'load_search_coupons';
        } else {
            return 'load_coupons';
        }
    }

    /**
     * Handle load more AJAX response
     */
    function handleLoadMoreResponse($btn, $loadMoreWrapper, $container, response) {
        endLoadingState($btn);

        if (response.success && response.data && response.data.content) {
            // Clean and parse the content more reliably
            let cleanContent = response.data.content.trim();

            // Remove HTML comments that might break jQuery parsing
            cleanContent = cleanContent.replace(/<!--[\s\S]*?-->/g, '');

            // Ensure content is wrapped in a container if it's not already
            if (!cleanContent.startsWith('<div') && !cleanContent.startsWith('<article')) {
                cleanContent = '<div>' + cleanContent + '</div>';
            }

            const $newContent = $(cleanContent);

            // Find the target container for new content
            let $targetContainer = $('#home-latest-coupons-grid-grid');

            // Fallback to other possible containers
            if ($targetContainer.length === 0) {
                $targetContainer = $container.find('.st-list-coupons, .coupon-list, .coupons-grid');
            }

            // Final fallback to container itself
            if ($targetContainer.length === 0) {
                $targetContainer = $container;
            }

            if ($newContent.length > 0) {
                $newContent.hide().appendTo($targetContainer).fadeIn(300);
            } else {
                // Fallback: append content directly as HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = cleanContent;
                const elements = Array.from(tempDiv.children);

                elements.forEach(element => {
                    element.style.display = 'none';
                    $targetContainer[0].appendChild(element);
                    $(element).fadeIn(300);
                });
            }

            updatePaginationData($btn, response.data);

            // Remove load more if no more pages
            if (shouldRemoveLoadMore(response.data)) {
                $loadMoreWrapper.fadeOut(300, function() {
                    $(this).remove();
                });
            }

            // Trigger custom event for other scripts
            $(document).trigger('ag-coupon:content-loaded', [$newContent]);
        } else {
            showLoadMoreError($btn);
        }
    }

    /**
     * Update pagination data after successful load
     */
    function updatePaginationData($btn, responseData) {
        if (responseData.next_page !== undefined) {
            $btn.attr('data-next-page', responseData.next_page);
            $btn.data('next-page', responseData.next_page);
        }

        if (responseData.current_page !== undefined) {
            $btn.attr('data-current-page', responseData.current_page);
            $btn.data('current-page', responseData.current_page);
        }
    }

    /**
     * Check if load more should be removed
     */
    function shouldRemoveLoadMore(responseData) {
        return responseData.next_page <= 0 ||
               responseData.has_more === false ||
               !responseData.content ||
               responseData.content.trim() === '';
    }

    /**
     * Show load more error message
     */
    function showLoadMoreError($btn) {
        const originalText = $btn.data('original-text');
        $btn.html('❌ خطأ في التحميل - حاول مرة أخرى');

        setTimeout(function() {
            if (originalText) {
                $btn.html(originalText);
            }
        }, 3000);
    }

    /**
     * Read More System for Store Descriptions
     */
    function initReadMoreSystem() {
        // Handle read more button clicks
        $(document).on('click', '.read-more-btn, #expandbtn', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $contentWrapper = $btn.closest('.store-content-wrapper');
            const $content = $contentWrapper.find('.store-content, #store-content');

            // Toggle expanded state
            if ($content.hasClass('expanded')) {
                // Collapse content
                $content.removeClass('expanded');
                $btn.html('إقرأ المزيد');

                // Add smooth scroll to top of content
                $('html, body').animate({
                    scrollTop: $content.offset().top - 100
                }, 300);
            } else {
                // Expand content
                $content.addClass('expanded');
                $btn.html('إقرأ أقل');
            }
        });
    }

    // Always available debug functions
    window.debugLoadMore = function() {
        // Check if AgCoupon is initialized
        if (window.AgCoupon) {
            console.log('AgCoupon initialized:', window.AgCoupon.initialized);
            console.log('AJAX URL:', window.AgCoupon.ajax_url);
            console.log('Nonce:', window.AgCoupon.nonce);
        }

        // Check for load more buttons
        const $buttons = $('.load-more-btn, .ag-load-more-coupons');
        console.log('Found load more buttons:', $buttons.length);
        $buttons.each(function(i) {
            const $btn = $(this);
            console.log('Button ' + i + ':', $btn.data());
        });

        // Check for target containers
        const $container = $('#latest-coupons-grid');
        console.log('Target container found:', $container.length > 0);

        // Test AJAX if possible
        if (window.AgCoupon && $buttons.length > 0) {
            console.log('Testing AJAX...');
        }
    };

    /**
     * Initialize everything when document is ready
     */
    $(document).ready(function() {
        // Initialize AgCoupon with ST object (should be available since we depend on wpcoupon_global)
        if (initializeAgCoupon()) {
            // Initialize all systems
            initVotingSystem();
            initRevealContent();
            initCouponClickHandler();
            initLoadMore();
            initReadMoreSystem();

            const $loadMoreBtns = $('.load-more-btn, .wpc-load-more, .button.wpc-load-more, .load-more .button');
        } else {
            // Fallback: wait for ST object if not immediately available
            setTimeout(function() {
                if (initializeAgCoupon()) {
                    initVotingSystem();
                    initRevealContent();
                    initCouponClickHandler();
                    initLoadMore();
                    initReadMoreSystem();
                }
            }, 500);
        }
    });

})(jQuery);
