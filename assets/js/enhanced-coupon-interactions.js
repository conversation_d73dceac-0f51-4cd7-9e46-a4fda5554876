/**
 * Enhanced Coupon Card Interactions
 * Scratch-to-reveal effects, voting, and tracking
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Enhanced Coupon Card Class
    class EnhancedCouponCard {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.initScratchEffect();
            this.initVoting();
            this.initTracking();
        }

        bindEvents() {
            // Scratch button click
            $(document).on('click', '.ag-scratch-button', this.handleScratchClick.bind(this));

            // Regular coupon button click
            $(document).on('click', '.ag-coupon-button', this.handleCouponClick.bind(this));

            // Coupon title click
            $(document).on('click', '.ag-coupon-title-link', this.handleTitleClick.bind(this));

            // Voting buttons
            $(document).on('click', '.ag-vote-up', this.handleVoteUp.bind(this));
            $(document).on('click', '.ag-vote-down', this.handleVoteDown.bind(this));

            // Load more coupons
            $(document).on('click', '.ag-load-more-coupons', this.handleLoadMore.bind(this));
        }

        initScratchEffect() {
            // Initialize scratch effect for scratch-style cards
            $('.ag-scratch-container').each(function() {
                const $container = $(this);
                const $button = $container.find('.ag-scratch-button');
                const $hiddenCode = $container.find('.ag-coupon-code-hidden');

                // Add scratch surface styling
                $button.css({
                    'background-image': 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)',
                    'background-size': '4px 4px',
                    'background-position': '0 0, 0 2px, 2px -2px, -2px 0px'
                });
            });
        }

        initVoting() {
            // Initialize voting system
            $('.ag-coupon-voting button').each(function() {
                const $button = $(this);
                $button.attr('title', $button.hasClass('ag-vote-up') ? 'إعجاب' : 'عدم إعجاب');
            });
        }

        initTracking() {
            // Initialize view tracking for coupons
            $('.ag-coupon-card').each(function() {
                const $card = $(this);
                const couponId = $card.data('coupon-id');

                // Track view when card comes into viewport
                if (typeof IntersectionObserver !== 'undefined') {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                this.trackCouponView(couponId);
                                observer.unobserve(entry.target);
                            }
                        });
                    }, { threshold: 0.5 });

                    observer.observe($card[0]);
                }
            });
        }

        handleScratchClick(e) {
            e.preventDefault();
            console.log('Scratch button clicked!');

            const $button = $(e.currentTarget);
            const $container = $button.closest('.ag-scratch-container');
            const $hiddenCode = $container.find('.ag-coupon-code-hidden');
            const couponId = $button.data('coupon-id');
            const affUrl = $button.data('aff-url');
            const code = $button.data('code');

            console.log('Scratch data:', { couponId, affUrl, code });

            // Prevent multiple scratches
            if ($button.hasClass('scratching')) {
                console.log('Already scratching, returning');
                return;
            }

            // Add scratching animation
            $button.addClass('scratching');

            // Track coupon click
            this.trackCouponClick(couponId);

            // Reveal code after animation
            setTimeout(() => {
                $hiddenCode.addClass('revealed');
                $button.hide();

                // Copy code to clipboard if available
                if (code && navigator.clipboard) {
                    navigator.clipboard.writeText(code).then(() => {
                        this.showNotification('تم نسخ الكود: ' + code, 'success');
                    });
                }

                // Open affiliate link after delay
                setTimeout(() => {
                    if (affUrl) {
                        window.open(affUrl, '_blank');
                    }
                }, 1000);

            }, 800);
        }

        handleCouponClick(e) {
            e.preventDefault();

            const $button = $(e.currentTarget);
            const couponId = $button.data('coupon-id');
            const affUrl = $button.data('aff-url');
            const code = $button.data('code');
            const type = $button.data('type');

            // Track coupon click
            this.trackCouponClick(couponId);

            // Handle different coupon types
            if (type === 'code' && code) {
                // Copy code to clipboard
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(code).then(() => {
                        this.showNotification('تم نسخ الكود: ' + code, 'success');
                    });
                }
            }

            // Open affiliate link
            if (affUrl) {
                window.open(affUrl, '_blank');
            }

            // Open coupon page in new tab after delay
            setTimeout(() => {
                const href = $button.attr('href');
                if (href) {
                    window.open(href, '_blank');
                }
            }, 500);
        }

        handleTitleClick(e) {
            e.preventDefault();

            const $link = $(e.currentTarget);
            const couponId = $link.data('coupon-id');
            const affUrl = $link.data('aff-url');
            const href = $link.attr('href');

            // Track coupon click
            this.trackCouponClick(couponId);

            // Open affiliate link first
            if (affUrl) {
                window.open(affUrl, '_blank');
            }

            // Then open coupon page
            setTimeout(() => {
                if (href) {
                    window.open(href, '_blank');
                }
            }, 300);
        }

        handleVoteUp(e) {
            e.preventDefault();
            e.stopPropagation();

            const $button = $(e.currentTarget);
            const couponId = $button.data('coupon-id');

            this.submitVote(couponId, 'up', $button);
        }

        handleVoteDown(e) {
            e.preventDefault();
            e.stopPropagation();

            const $button = $(e.currentTarget);
            const couponId = $button.data('coupon-id');

            this.submitVote(couponId, 'down', $button);
        }

        handleLoadMore(e) {
            e.preventDefault();

            const $button = $(e.currentTarget);
            const originalText = $button.text();

            // Show loading state
            $button.text('جاري التحميل...').prop('disabled', true);

            // Simulate loading (integrate with existing AJAX functions)
            setTimeout(() => {
                $button.text(originalText).prop('disabled', false);
                this.showNotification('تم تحميل المزيد من الكوبونات', 'success');
            }, 2000);
        }

        trackCouponView(couponId) {
            // Track coupon view using existing theme AJAX
            if (typeof wpcoupon_ajax_url !== 'undefined') {
                $.ajax({
                    url: wpcoupon_ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wpcoupon_track_view',
                        coupon_id: couponId,
                        nonce: wpcoupon_nonce
                    },
                    success: function(response) {
                        // View tracked successfully
                    }
                });
            }
        }

        trackCouponClick(couponId) {
            // Track coupon click using existing theme AJAX
            if (typeof wpcoupon_ajax_url !== 'undefined') {
                $.ajax({
                    url: wpcoupon_ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wpcoupon_track_click',
                        coupon_id: couponId,
                        nonce: wpcoupon_nonce
                    },
                    success: function(response) {
                        // Click tracked successfully
                    }
                });
            }
        }

        submitVote(couponId, voteType, $button) {
            // Prevent multiple votes
            if ($button.hasClass('voted')) {
                return;
            }

            // Mark as voted
            $button.addClass('voted');

            // Submit vote using existing theme AJAX
            if (typeof wpcoupon_ajax_url !== 'undefined') {
                $.ajax({
                    url: wpcoupon_ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wpcoupon_submit_vote',
                        coupon_id: couponId,
                        vote_type: voteType,
                        nonce: wpcoupon_nonce
                    },
                    success: (response) => {
                        if (response.success) {
                            // Update vote count
                            const $count = $button.next('span');
                            const currentCount = parseInt($count.text()) || 0;
                            $count.text(currentCount + 1);

                            // Show feedback
                            $button.addClass(voteType === 'up' ? 'text-green-500' : 'text-red-500');
                            this.showNotification('شكراً لتقييمك!', 'success');
                        }
                    },
                    error: () => {
                        $button.removeClass('voted');
                        this.showNotification('حدث خطأ، حاول مرة أخرى', 'error');
                    }
                });
            }
        }

        showNotification(message, type = 'info') {
            // Create notification element
            const $notification = $(`
                <div class="ag-notification fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }">
                    ${message}
                </div>
            `);

            // Add to body
            $('body').append($notification);

            // Animate in
            setTimeout(() => {
                $notification.removeClass('translate-x-full');
            }, 100);

            // Remove after delay
            setTimeout(() => {
                $notification.addClass('translate-x-full');
                setTimeout(() => {
                    $notification.remove();
                }, 300);
            }, 3000);
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Enhanced Coupon Interactions: Initializing...');
        new EnhancedCouponCard();
        console.log('Enhanced Coupon Interactions: Initialized successfully');
    });

})(jQuery);
