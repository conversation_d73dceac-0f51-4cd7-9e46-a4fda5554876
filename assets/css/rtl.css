/*
 * RTL (Right-to-Left) Styles for Ag-Coupon Theme
 *
 * Optimized RTL styles with no duplications
 * Font Family: Readex Pro (Google Fonts)
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   BASE RTL STYLES
   ========================================================================== */

/* RTL Direction and Typography */
body.rtl,
html[dir="rtl"] body {
    direction: rtl;
    text-align: right;
    font-family: 'Readex Pro', 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    line-height: 1.7;
    letter-spacing: 0;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
    text-rendering: optimizeLegibility;
}

/* RTL Typography */
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
    font-family: 'Readex Pro', sans-serif;
    font-weight: 600;
    line-height: 1.6;
    letter-spacing: 0;
}

.rtl p {
    line-height: 1.8;
}

/* ==========================================================================
   HEADER AND NAVIGATION - RTL
   ========================================================================== */

/* Header Content */
.rtl .header-content {
    direction: rtl;
}

/* Search Input */
.rtl .search-input-container input {
    text-align: right;
    padding-right: 1rem;
    padding-left: 3rem;
}

.rtl .search-input-container .absolute.right-0 {
    right: auto;
    left: 0;
}

.rtl .search-input-container .absolute.left-2 {
    left: 0.5rem;
    right: auto;
}

/* ==========================================================================
   MEGA MENU AND NAVIGATION - RTL
   ========================================================================== */

/* Navigation Menu */
.rtl .nav-menu {
    direction: rtl;
}

/* Mega Menu */
.rtl .mega-menu {
    direction: rtl;
    text-align: right;
}

.rtl .mega-menu-column h3::after {
    left: auto;
    right: 0;
}

/* Menu Items */
.rtl .menu-item-enhanced a {
    direction: rtl;
}

.rtl .menu-item-enhanced .flex-shrink-0 {
    margin-right: 0;
    margin-left: 0.75rem;
}

/* Mega Menu Items */
.rtl .column-items a {
    flex-direction: row-reverse !important;
    text-align: right !important;
}

.rtl .column-items a svg,
.rtl .column-items a img,
.rtl .column-items a i {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

/* Mobile Menu Items */
.rtl .mobile-item-link {
    flex-direction: row-reverse !important;
}

.rtl .mobile-item-link svg,
.rtl .mobile-item-link img {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

/* ==========================================================================
   MOBILE MENU - RTL
   ========================================================================== */

.rtl #mobile-menu-panel {
    right: auto;
    left: 0;
    transform: translateX(-100%);
}

.rtl #mobile-menu-panel.translate-x-full {
    transform: translateX(-100%);
}

.rtl #mobile-menu-panel:not(.translate-x-full) {
    transform: translateX(0);
}

/* ==========================================================================
   CONTENT AND COMPONENTS - RTL
   ========================================================================== */

/* Content Layout */
.rtl .content-article {
    direction: rtl;
    text-align: right;
}

/* Post Meta */
.rtl .post-meta {
    direction: rtl;
}

.rtl .post-meta .flex {
    flex-direction: row-reverse;
}

.rtl .post-meta svg {
    margin-right: 0;
    margin-left: 0.25rem;
}

/* ==========================================================================
   COUPON AND STORE CARDS - RTL
   ========================================================================== */

/* Coupon Cards */
.rtl .coupon-card {
    direction: rtl;
    text-align: right;
}

.rtl .coupon-header {
    direction: rtl;
}

.rtl .coupon-store-logo {
    margin-right: 0;
    margin-left: 1rem;
}

.rtl .coupon-actions {
    direction: rtl;
}

.rtl .coupon-code {
    direction: ltr; /* Keep coupon codes LTR for readability */
    text-align: center;
}

/* Essential Store Styles Only */
.rtl .store-content {
    direction: rtl;
    text-align: right;
}

/* RTL Slider Styles */
.rtl .featured-stores-slider-container {
    direction: rtl;
}

.rtl .featured-stores-slider .splide__list {
    direction: rtl;
}

.rtl .featured-stores-slider .splide__slide {
    direction: rtl;
}

.rtl .featured-stores-slider .splide__pagination {
    direction: rtl;
}

/* ==========================================================================
   FORM ELEMENTS AND BUTTONS - RTL
   ========================================================================== */

/* Form Elements */
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="search"],
.rtl textarea,
.rtl select {
    direction: rtl;
    text-align: right;
}

.rtl .form-input {
    direction: rtl;
    text-align: right;
}

/* Buttons */
.rtl .btn-primary,
.rtl .btn-secondary {
    direction: rtl;
}

.rtl .btn-primary svg,
.rtl .btn-secondary svg {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* ==========================================================================
   MISCELLANEOUS RTL STYLES
   ========================================================================== */

/* Pagination */
.rtl .pagination-nav {
    direction: rtl;
}

/* Breadcrumbs */
.rtl .breadcrumbs {
    direction: rtl;
}

.rtl .breadcrumb-separator {
    transform: scaleX(-1);
}

/* Tables */
.rtl table {
    direction: rtl;
}

.rtl th,
.rtl td {
    text-align: right;
}

/* Sidebar and Widgets */
.rtl .sidebar {
    direction: rtl;
}

.rtl .widget {
    direction: rtl;
    text-align: right;
}

/* Footer */
.rtl .site-footer {
    direction: rtl;
    text-align: right;
}

.rtl .footer-widget {
    direction: rtl;
    text-align: right;
}

/* Comments */
.rtl .comments-content {
    direction: rtl;
    text-align: right;
}

.rtl .comment {
    direction: rtl;
    text-align: right;
}

/* ==========================================================================
   RESPONSIVE DESIGN - RTL
   ========================================================================== */

/* Mobile Styles */
@media (max-width: 768px) {
    .rtl .container {
        padding-right: 1rem;
        padding-left: 1rem;
    }

    .rtl .header-content {
        padding: 0.5rem 1rem;
    }

    .rtl .mobile-menu-toggle {
        order: -1;
    }

    .rtl .search-input-container {
        order: 1;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .rtl .container {
        padding-right: 0.75rem;
        padding-left: 0.75rem;
    }

    .rtl h1 { font-size: 1.875rem; }
    .rtl h2 { font-size: 1.5rem; }
    .rtl h3 { font-size: 1.25rem; }
}