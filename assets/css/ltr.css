/*
 * LTR (Left-to-Right) Consolidated Styles for Ag-Coupon Theme
 *
 * This file contains ALL LTR-specific styles including:
 * - Base LTR styles
 * - Enhanced components
 * - Mobile optimizations
 * - Mega menu LTR styles
 *
 * Font Family: Jost (Google Fonts)
 * Direction: LTR
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   BASE STYLES - LTR
   ========================================================================== */

/* Root Variables for LTR */
:root {
    --font-primary: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Inter', system-ui, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Spacing for LTR */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Body and HTML - LTR */
html[dir="ltr"] {
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body.ltr {
    font-family: var(--font-primary);
    direction: ltr;
    text-align: left;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
    line-height: 1.6;
    letter-spacing: -0.01em;
}

/* Typography - LTR */
.ltr h1, .ltr h2, .ltr h3, .ltr h4, .ltr h5, .ltr h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    text-rendering: optimizeLegibility;
}

.ltr h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
.ltr h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
.ltr h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
.ltr h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }
.ltr h5 { font-size: clamp(1.125rem, 2vw, 1.25rem); }
.ltr h6 { font-size: clamp(1rem, 1.5vw, 1.125rem); }

.ltr p {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.7;
    margin-bottom: 1.25rem;
}

/* ==========================================================================
   HEADER STYLES - LTR
   ========================================================================== */

/* Header Container */
.ltr .site-header {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.8);
}

/* Logo Area - LTR */
.ltr .logo-area {
    margin-right: var(--space-lg);
}

.ltr .logo-area img {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ltr .logo-area:hover img {
    transform: scale(1.05);
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

/* Navigation - LTR */
.ltr .main-navigation {
    margin-left: var(--space-xl);
}

.ltr .main-navigation ul {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    list-style: none;
    margin: 0;
    padding: 0;
}

.ltr .main-navigation li {
    position: relative;
}

.ltr .main-navigation a {
    font-weight: 500;
    font-size: 0.95rem;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.ltr .main-navigation a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(252, 211, 77, 0.1), transparent);
    transition: left 0.5s ease;
}

.ltr .main-navigation a:hover::before {
    left: 100%;
}

/* Search Area - LTR */
.ltr .search-area {
    margin: 0 var(--space-xl);
}

.ltr .search-container input {
    padding-left: var(--space-md);
    padding-right: 3.5rem;
    text-align: left;
}

.ltr .search-container button {
    right: var(--space-sm);
    left: auto;
}

/* ==========================================================================
   ENHANCED MEGA MENU STYLES - LTR
   ========================================================================== */

.ltr .mega-menu {
    left: 0;
    right: auto;
    transform-origin: top left;
    text-align: left;
}

.ltr .mega-menu-column {
    padding-right: var(--space-lg);
    border-right: 1px solid rgba(229, 231, 235, 0.3);
}

.ltr .mega-menu-column:last-child {
    padding-right: 0;
    border-right: none;
}

.ltr .column-header {
    text-align: left;
}

.ltr .column-header span {
    margin-right: auto;
    margin-left: 0;
}

.ltr .menu-item-enhanced {
    margin-bottom: var(--space-sm);
    border-radius: var(--radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ltr .menu-item-enhanced a {
    text-align: left;
    padding: var(--space-md);
}

.ltr .menu-item-enhanced a:hover {
    transform: translateX(4px);
}

/* LTR Mega Menu Items */
.ltr .column-items a svg,
.ltr .column-items a img,
.ltr .column-items a i {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ltr .column-items a {
    flex-direction: row !important;
    text-align: left !important;
}

/* LTR Mobile Menu Items */
.ltr .mobile-item-link svg,
.ltr .mobile-item-link img {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ltr .mobile-item-link {
    flex-direction: row !important;
}

/* LTR Navigation */
.ltr .nav-menu {
    direction: ltr !important;
}

.ltr .mega-menu .grid {
    direction: ltr !important;
}

.ltr .mega-menu-column {
    text-align: left !important;
}

/* ==========================================================================
   MOBILE MENU - LTR
   ========================================================================== */

.ltr #mobile-menu-panel {
    left: 0;
    right: auto;
    transform: translateX(-100%);
}

.ltr .mobile-menu-open #mobile-menu-panel {
    transform: translateX(0);
}

.ltr .mobile-menu-header {
    padding: var(--space-lg);
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.ltr .mobile-menu-close {
    margin-left: auto;
    margin-right: 0;
}

/* ==========================================================================
   SEARCH RESULTS - LTR
   ========================================================================== */

.ltr .search-results-container {
    left: 0;
    right: auto;
    text-align: left;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    box-shadow: var(--shadow-2xl);
}

.ltr .search-results-stores a,
.ltr .search-results-coupons a {
    padding: var(--space-md);
    display: flex;
    align-items: center;
    text-decoration: none;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ltr .search-results-stores a:hover,
.ltr .search-results-coupons a:hover {
    background-color: rgba(252, 211, 77, 0.05);
    transform: translateX(4px);
}

.ltr .search-results-stores .flex-shrink-0,
.ltr .search-results-coupons .flex-shrink-0 {
    margin-right: var(--space-md);
    margin-left: 0;
}

/* ==========================================================================
   ACCESSIBILITY ENHANCEMENTS - LTR
   ========================================================================== */

/* Focus Styles */
.ltr *:focus {
    outline: 2px solid #FCD34D;
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

.ltr *:focus:not(:focus-visible) {
    outline: none;
}

.ltr *:focus-visible {
    outline: 2px solid #FCD34D;
    outline-offset: 2px;
}

/* Skip Links */
.ltr .skip-link {
    position: absolute;
    left: -9999px;
    top: 0;
    z-index: 999999;
    padding: var(--space-sm) var(--space-md);
    background: #1E40AF;
    color: white;
    text-decoration: none;
    border-radius: 0 0 var(--radius-md) 0;
}

.ltr .skip-link:focus {
    left: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .ltr .main-navigation a {
        border: 1px solid currentColor;
    }

    .ltr .search-container input {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .ltr * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .ltr .logo-area:hover img {
        transform: none;
    }

    .ltr .main-navigation a::before {
        display: none;
    }
}

/* ==========================================================================
   CONTENT AREA STYLES - LTR
   ========================================================================== */

/* Main Content */
.ltr .site-content {
    padding-top: var(--space-xl);
}

.ltr .container {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
}

/* ==========================================================================
   COUPON CARD STYLES - LTR
   ========================================================================== */

.ltr .coupon-card {
    text-align: left;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ltr .coupon-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.ltr .coupon-card-header {
    border-bottom: 1px solid rgba(229, 231, 235, 0.8);
}



.ltr .coupon-badge-code,
.ltr .coupon-badge-sale,
.ltr .coupon-badge-print {
    margin-left: auto;
    margin-right: 0;
}

.ltr .coupon-code-container {
    border-radius: var(--radius-lg);
    border: 2px dashed rgba(252, 211, 77, 0.3);
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.05), rgba(252, 211, 77, 0.1));
}

.ltr .copy-code-btn {
    margin-left: var(--space-md);
    margin-right: 0;
}

/* ==========================================================================
   ESSENTIAL STORE STYLES - LTR
   ========================================================================== */

.ltr .store-content {
    text-align: left;
}

/* LTR Slider Styles */
.ltr .featured-stores-slider-container {
    direction: ltr;
}

.ltr .featured-stores-slider .splide__list {
    direction: ltr;
}

.ltr .featured-stores-slider .splide__slide {
    direction: ltr;
}

.ltr .featured-stores-slider .splide__pagination {
    direction: ltr;
}

/* ==========================================================================
   BUTTON STYLES - LTR
   ========================================================================== */

.ltr .btn-primary,
.ltr .btn-secondary {
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.ltr .btn-primary::before,
.ltr .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ltr .btn-primary:hover::before,
.ltr .btn-secondary:hover::before {
    left: 100%;
}

.ltr .btn-primary:hover,
.ltr .btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   FOOTER STYLES - LTR
   ========================================================================== */

.ltr .site-footer {
    text-align: left;
}

.ltr .footer-column {
    padding-right: var(--space-lg);
    padding-left: 0;
}

.ltr .footer-column:last-child {
    padding-right: 0;
}

.ltr .footer-widget-content h3,
.ltr .footer-widget-content h4 {
    margin-bottom: var(--space-md);
    position: relative;
}

.ltr .footer-widget-content h3::after,
.ltr .footer-widget-content h4::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #FCD34D, #1E40AF);
    border-radius: 1px;
}

.ltr .footer-widget-content ul li {
    padding-left: var(--space-md);
    position: relative;
}

.ltr .footer-widget-content ul li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #FCD34D;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.ltr .footer-widget-content ul li:hover::before {
    transform: translateX(4px);
}

.ltr .footer-copyright {
    text-align: left;
}

.ltr .footer-social-links {
    margin-left: auto;
    margin-right: 0;
}

.ltr .footer-social-links a {
    margin-left: var(--space-sm);
    margin-right: 0;
}

/* ==========================================================================
   BACK TO TOP BUTTON - LTR
   ========================================================================== */

.ltr #back-to-top {
    left: var(--space-lg);
    right: auto;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ltr #back-to-top:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* ==========================================================================
   RESPONSIVE DESIGN - LTR
   ========================================================================== */

/* Tablet Styles */
@media (max-width: 1024px) {
    .ltr .main-navigation {
        margin-left: var(--space-lg);
    }

    .ltr .search-area {
        margin: 0 var(--space-lg);
    }

    .ltr .logo-area {
        margin-right: var(--space-md);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .ltr .container {
        padding-left: var(--space-sm);
        padding-right: var(--space-sm);
    }

    .ltr .site-content {
        padding-top: var(--space-lg);
    }

    .ltr .footer-column {
        padding-right: 0;
        margin-bottom: var(--space-xl);
    }

    .ltr .footer-copyright {
        text-align: center;
    }

    .ltr .footer-social-links {
        margin: var(--space-md) auto 0;
        justify-content: center;
    }

    .ltr #back-to-top {
        left: var(--space-md);
        bottom: var(--space-md);
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .ltr .container {
        padding-left: var(--space-xs);
        padding-right: var(--space-xs);
    }

    .ltr h1 { font-size: 1.875rem; }
    .ltr h2 { font-size: 1.5rem; }
    .ltr h3 { font-size: 1.25rem; }

    .ltr .coupon-card {
        margin-bottom: var(--space-md);
    }
}

/* ==========================================================================
   PERFORMANCE OPTIMIZATIONS - LTR
   ========================================================================== */

/* GPU Acceleration for Smooth Animations */
.ltr .coupon-card,
.ltr .btn-primary,
.ltr .btn-secondary,
.ltr .logo-area img,
.ltr #back-to-top {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Optimize Font Loading */
.ltr {
    font-display: swap;
}

/* ==========================================================================
   PRINT STYLES - LTR
   ========================================================================== */

@media print {
    .ltr .site-header,
    .ltr .site-footer,
    .ltr #back-to-top,
    .ltr .search-area,
    .ltr .mobile-menu-toggle {
        display: none !important;
    }

    .ltr .site-content {
        padding-top: 0;
    }

    .ltr .coupon-card {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }

    .ltr a {
        text-decoration: underline;
    }

    .ltr a[href^="http"]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
}
body:not(.rtl) {
    direction: ltr;
    font-family: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    text-align: left;
}

/* LTR Typography */
.ltr h1, .ltr h2, .ltr h3, .ltr h4, .ltr h5, .ltr h6,
h1, h2, h3, h4, h5, h6 {
    font-family: 'Jost', sans-serif;
    font-weight: 600;
}

.ltr .site-title,
.site-title {
    font-family: 'Jost', sans-serif;
    font-weight: 700;
}

/* LTR Header */
.ltr .header-content,
.header-content {
    direction: ltr;
}

.ltr .search-input-container input,
.search-input-container input {
    text-align: left;
    padding-left: 1rem;
    padding-right: 3rem;
}

/* LTR Navigation */
.ltr .nav-menu,
.nav-menu {
    direction: ltr;
}

.ltr .mega-menu,
.mega-menu {
    direction: ltr;
    text-align: left;
}

.ltr .menu-item-enhanced a,
.menu-item-enhanced a {
    direction: ltr;
}

/* LTR Mobile Menu */
.ltr #mobile-menu-panel,
#mobile-menu-panel {
    right: 0;
    left: auto;
    transform: translateX(100%);
}

/* LTR Content Layout */
.ltr .content-article,
.content-article {
    direction: ltr;
    text-align: left;
}

.ltr .post-meta,
.post-meta {
    direction: ltr;
}

/* LTR Coupon Cards */
.ltr .coupon-card,
.coupon-card {
    direction: ltr;
    text-align: left;
}

.ltr .coupon-header,
.coupon-header {
    direction: ltr;
}

.ltr .coupon-store-logo,
.coupon-store-logo {
    margin-left: 0;
    margin-right: 1rem;
}



/* LTR Sidebar */
.ltr .sidebar,
.sidebar {
    direction: ltr;
}

.ltr .widget,
.widget {
    direction: ltr;
    text-align: left;
}

/* LTR Footer */
.ltr .site-footer,
.site-footer {
    direction: ltr;
    text-align: left;
}

.ltr .footer-widget,
.footer-widget {
    direction: ltr;
    text-align: left;
}

/* LTR Form Elements */
.ltr input[type="text"],
.ltr input[type="email"],
.ltr input[type="search"],
.ltr textarea,
.ltr select,
input[type="text"],
input[type="email"],
input[type="search"],
textarea,
select {
    direction: ltr;
    text-align: left;
}

.ltr .form-input,
.form-input {
    direction: ltr;
    text-align: left;
}

/* LTR Buttons */
.ltr .btn-primary,
.ltr .btn-secondary,
.btn-primary,
.btn-secondary {
    direction: ltr;
}

/* LTR Pagination */
.ltr .pagination-nav,
.pagination-nav {
    direction: ltr;
}

/* LTR Tables */
.ltr table,
table {
    direction: ltr;
}

.ltr th,
.ltr td,
th,
td {
    text-align: left;
}

/* LTR Comments */
.ltr .comments-content,
.comments-content {
    direction: ltr;
    text-align: left;
}

/* LTR Search Results */
.ltr .search-results,
.search-results {
    direction: ltr;
    text-align: left;
}

/* LTR Responsive Adjustments */
@media (max-width: 768px) {
    .ltr .mobile-search-overlay,
    .mobile-search-overlay {
        direction: ltr;
    }

    .ltr .mobile-search-overlay input,
    .mobile-search-overlay input {
        text-align: left;
        padding-left: 1rem;
        padding-right: 4rem;
    }

    .ltr .mobile-menu-panel,
    .mobile-menu-panel {
        direction: ltr;
        text-align: left;
    }
}

/* LTR Print Styles */
@media print {
    .ltr body,
    body {
        direction: ltr;
        font-family: 'Jost', serif;
    }
}
