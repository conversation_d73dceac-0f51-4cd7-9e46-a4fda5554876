/* ==========================================================================
   AG-COUPON HOME PAGE STYLES
   Modern, Creative Landing Page Design
   ========================================================================== */

/* ==========================================================================
   HERO SECTION
   ========================================================================== */

.hero-section {
    background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 50%, #FCD34D 100%);
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-highlight {
    background: linear-gradient(45deg, #FCD34D, #F59E0B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #FCD34D;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 400px;
}

.hero-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: float 6s ease-in-out infinite;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 120px;
}

.floating-card.card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    top: 50%;
    right: 40%;
    animation-delay: 2s;
}

.floating-card.card-3 {
    bottom: 20%;
    right: 20%;
    animation-delay: 4s;
}

.card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.card-text {
    font-weight: 600;
    color: #1E40AF;
    font-size: 0.875rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(-10px) rotate(-1deg); }
}

/* ==========================================================================
   SECTION STYLES
   ========================================================================== */

.section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1E40AF;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, #FCD34D, #F59E0B);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ==========================================================================
   FEATURED STORES SECTION
   ========================================================================== */

.featured-stores-section {
    background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.store-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.store-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.featured-store {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #FCD34D, #F59E0B) border-box;
}

.store-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #FCD34D, #F59E0B);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.store-image {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 1rem;
    overflow: hidden;
    background: #F3F4F6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.store-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1E40AF, #3B82F6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.store-info {
    text-align: center;
}

.store-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.store-coupons {
    color: #6B7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.store-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(45deg, #1E40AF, #3B82F6);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.store-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(30, 64, 175, 0.3);
}

.link-arrow {
    width: 1rem;
    height: 1rem;
    transition: transform 0.2s ease;
}

.store-link:hover .link-arrow {
    transform: translateX(4px);
}

/* ==========================================================================
   LATEST COUPONS SECTION
   ========================================================================== */

.latest-coupons-section {
    background: white;
}

.coupons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.coupon-card {
    background: white;
    border: 1px solid #E5E7EB;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.coupon-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    border-color: #FCD34D;
}

.coupon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #F9FAFB;
    border-bottom: 1px solid #E5E7EB;
}

.store-logo {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    overflow: hidden;
    background: #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.store-logo span {
    font-weight: 600;
    color: #6B7280;
    font-size: 0.875rem;
}

.type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.type-badge.code {
    background: #DBEAFE;
    color: #1E40AF;
}

.type-badge.deal {
    background: #FEF3C7;
    color: #D97706;
}

.coupon-content {
    padding: 1rem;
    position: relative;
}

.coupon-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.coupon-description {
    color: #6B7280;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.discount-badge {
    position: absolute;
    top: -0.5rem;
    right: 1rem;
    background: linear-gradient(45deg, #EF4444, #DC2626);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 700;
    font-size: 0.875rem;
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.coupon-footer {
    padding: 1rem;
    background: #F9FAFB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coupon-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.store-name {
    font-weight: 600;
    color: #1F2937;
    font-size: 0.875rem;
}

.expires {
    font-size: 0.75rem;
    color: #EF4444;
}

.coupon-button {
    background: linear-gradient(45deg, #10B981, #059669);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.coupon-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

/* ==========================================================================
   CATEGORIES SECTION
   ========================================================================== */

.categories-section {
    background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);
    border-color: #FCD34D;
}

.category-image {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 1rem;
    overflow: hidden;
    background: linear-gradient(45deg, #1E40AF, #3B82F6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-icon {
    color: white;
}

.category-icon svg {
    width: 2rem;
    height: 2rem;
}

.category-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.category-count {
    color: #6B7280;
    font-size: 0.875rem;
}

/* ==========================================================================
   POPULAR STORES SECTION
   ========================================================================== */

.popular-stores-section {
    background: white;
}

.popular-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.popular-store {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.popular-store .store-image {
    width: 100%;
    height: 120px;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
}

.store-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(30, 64, 175, 0.9), rgba(59, 130, 246, 0.9));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
    border-radius: 1rem;
}

.popular-store:hover .store-overlay {
    opacity: 1;
}

/* ==========================================================================
   NEWSLETTER SECTION
   ========================================================================== */

.newsletter-section {
    background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 50%, #FCD34D 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.newsletter-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.newsletter-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.newsletter-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.feature-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #FCD34D;
}

.subscription-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.form-group input {
    flex: 1;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.form-group input:focus {
    outline: none;
    border-color: #FCD34D;
    box-shadow: 0 0 0 3px rgba(252, 211, 77, 0.3);
}

.subscribe-btn {
    background: linear-gradient(45deg, #FCD34D, #F59E0B);
    color: #1F2937;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.subscribe-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(252, 211, 77, 0.3);
}

.btn-icon {
    width: 1rem;
    height: 1rem;
}

.form-note {
    font-size: 0.75rem;
    opacity: 0.8;
    text-align: center;
    line-height: 1.4;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1rem;
    }

    .hero-visual {
        height: 200px;
    }

    .floating-card {
        padding: 1rem;
        min-width: 100px;
    }

    .section-container {
        padding: 2rem 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .featured-grid,
    .coupons-grid,
    .categories-grid,
    .popular-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .newsletter-title {
        font-size: 2rem;
    }

    .form-group {
        flex-direction: column;
    }

    .newsletter-features {
        justify-content: center;
    }
}
