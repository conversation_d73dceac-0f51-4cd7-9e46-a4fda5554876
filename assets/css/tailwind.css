@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * Ag-Coupon Theme - Pixel Perfect Design System
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 *
 * Features:
 * - Responsive Design (Mobile-First)
 * - RTL/LTR Support
 * - Accessibility (WCAG 2.1 AA)
 * - Performance Optimized
 * - SEO Enhanced
 */

@layer base {
  /* ==========================================================================
     PERFORMANCE & SEO OPTIMIZATIONS
     ========================================================================== */

  /* Critical Rendering Path Optimization */
  html {
    font-display: swap;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  /* Base Typography for SEO */
  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: theme('colors.gray.900');
    background-color: theme('colors.gray.50');
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    font-variant-ligatures: common-ligatures;
  }

  /* Semantic HTML Enhancement */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: theme('colors.gray.900');
    margin-bottom: 0.75em;
  }

  /* Improved Link Accessibility */
  a {
    color: theme('colors.primary.600');
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: theme('colors.primary.700');
    text-decoration: underline;
  }

  a:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
    border-radius: 0.25rem;
  }

  /* Enhanced Form Elements */
  input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  /* Image Optimization */
  img {
    max-width: 100%;
    height: auto;
    display: block;
    opacity: 1; /* Ensure all images are visible by default */
  }

  /* Lazy Loading Support - Fixed */
  img[loading="lazy"] {
    opacity: 1 !important; /* Force visibility for lazy-loaded images */
    transition: opacity 0.3s ease;
  }

  /* Only hide images with explicit lazy class for custom implementations */
  img.lazy-loading:not(.loaded) {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  img.lazy-loading.loaded,
  img.loaded {
    opacity: 1 !important;
  }

  /* Coupon and Store Card Images - Force Visibility */
  .coupon-card img,
  .store-card img,
  .coupon-image img,
  .store-image img {
    opacity: 1 !important;
    display: block !important;
  }
}




.skip-link {
    position: absolute;
    right: -9999px;
    top: 0px;
    z-index: 999999;
    color: white;
    padding: var(--space-sm-rtl) var(--space-md-rtl);
    background: rgb(30, 64, 175);
    text-decoration: none;
    border-radius: 0 0 0 var(--radius-md);
}

@layer components {
  /* Coupon Card Components */
  .coupon-card {
    @apply bg-white rounded-xl shadow-coupon hover:shadow-coupon-hover transition-all duration-300 overflow-hidden border border-gray-100;
  }

  .coupon-card-header {
    @apply p-4 border-b border-gray-100;
  }

  .coupon-card-body {
    @apply p-4;
  }

  .coupon-card-footer {
    @apply p-4 bg-gray-50 border-t border-gray-100;
  }

  /* Coupon Type Badges */
  .coupon-badge-code {
    @apply bg-primary-300 text-primary-900 px-3 py-1 rounded-full text-sm font-semibold;
  }

  .coupon-badge-sale {
    @apply bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold;
  }

  .coupon-badge-print {
    @apply bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold;
  }

  .coupon-badge-expired {
    @apply bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold;
  }

  .coupon-badge-featured {
    @apply bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold;
  }



  /* Button Components */
  .btn-primary {
    @apply bg-primary-300 hover:bg-primary-400 text-primary-900 font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-outline {
    @apply border-2 border-primary-300 text-primary-700 hover:bg-primary-300 hover:text-primary-900 font-semibold py-2 px-4 rounded-lg transition-all duration-200;
  }

  /* Form Components */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent;
  }

  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent bg-white;
  }

  /* Navigation Components */
  .nav-link {
    @apply text-gray-700 hover:text-secondary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply text-secondary-600 bg-secondary-50 px-3 py-2 rounded-md text-sm font-medium;
  }
}

@layer utilities {
  /* Base utilities - RTL/LTR handled by separate CSS files */

  /* Gradient utilities with theme colors */
  .bg-gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.300'), theme('colors.primary.400'));
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, theme('colors.secondary.500'), theme('colors.secondary.600'));
  }

  /* Text gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.400'), theme('colors.primary.600'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: linear-gradient(135deg, theme('colors.secondary.500'), theme('colors.secondary.700'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Header scroll effect */
  .header-scrolled {
    @apply shadow-xl bg-white/95 backdrop-blur-sm;
  }

  /* Enhanced Mega Menu Styles */
  .mega-menu {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .mega-menu::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid theme('colors.primary.400');
  }

  .mega-menu-column h3::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, theme('colors.primary.400'), theme('colors.secondary.500'));
    border-radius: 1px;
  }

  .menu-item-enhanced:hover {
    transform: translateY(-1px);
  }

  .menu-item-enhanced a:hover .flex-shrink-0 {
    transform: scale(1.05);
  }

  /* Header scroll effect enhancement */
  .site-header.scrolled {
    @apply shadow-xl;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  /* Mobile menu animations */
  .mobile-menu-open #mobile-menu-panel {
    transform: translateX(0);
  }

  .mobile-menu-open .hamburger-menu span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .mobile-menu-open .hamburger-menu span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-open .hamburger-menu span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Footer widget styling */
  .footer-widget-content h3,
  .footer-widget-content h4 {
    @apply text-white font-bold mb-4 text-lg;
  }

  .footer-widget-content ul {
    @apply space-y-2;
  }

  .footer-widget-content ul li a {
    @apply text-gray-300 hover:text-primary-300 transition-colors duration-200 text-sm;
  }

  .footer-widget-content p {
    @apply text-gray-300 text-sm leading-relaxed;
  }

  /* Back to top button */
  #back-to-top {
    @apply fixed bottom-6 left-6 z-40 opacity-0 invisible transition-all duration-300;
  }

  #back-to-top.show {
    @apply opacity-100 visible;
  }

  /* Coupon Instructions Tabs */
  .tab-content {
    @apply transition-all duration-300 ease-in-out;
  }

  .tab-content.hidden {
    @apply opacity-0;
  }

  .tab-content.active {
    @apply opacity-100;
  }

  /* Essential Store Styles Only */
  .store-content {
    @apply transition-all duration-300 ease-in-out;
  }

  .store-content.readmore {
    @apply max-h-none;
  }

  .store-content:not(.readmore) {
    @apply max-h-24 overflow-hidden;
  }

  /* Store tabs styling */
  .coupon-filter-tabs .tab-button.active {
    @apply bg-primary-300 text-primary-900;
  }

  .coupon-filter-tabs .tab-button:not(.active) {
    @apply text-gray-600 hover:bg-gray-100;
  }

  /* Splide Slider Customization - RTL/LTR Compatible */
  .featured-stores-slider .splide__arrow {
    @apply bg-white shadow-lg border border-gray-200 text-gray-600 hover:text-primary-600 hover:border-primary-300 transition-all duration-200;
    width: 3rem;
    height: 3rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  .featured-stores-slider .splide__arrow:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  .featured-stores-slider .splide__pagination__page {
    @apply bg-gray-300 hover:bg-primary-400 transition-colors duration-200;
  }

  .featured-stores-slider .splide__pagination__page.is-active {
    @apply bg-primary-500;
  }

  .featured-stores-slider .splide__track {
    @apply overflow-visible;
  }

  /* RTL Slider Specific Styles */
  .featured-stores-slider.rtl .splide__arrow--prev {
    right: -1.5rem;
    left: auto;
  }

  .featured-stores-slider.rtl .splide__arrow--next {
    left: -1.5rem;
    right: auto;
  }

  /* LTR Slider Specific Styles */
  .featured-stores-slider.ltr .splide__arrow--prev {
    left: -1.5rem;
    right: auto;
  }

  .featured-stores-slider.ltr .splide__arrow--next {
    right: -1.5rem;
    left: auto;
  }

  /* Slider Container Direction */
  .featured-stores-slider-container[dir="rtl"] {
    direction: rtl;
  }

  .featured-stores-slider-container[dir="ltr"] {
    direction: ltr;
  }




}
