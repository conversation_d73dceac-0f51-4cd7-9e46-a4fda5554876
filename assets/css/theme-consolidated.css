/*
 * Ag-Coupon Theme - Consolidated Styles
 *
 * This file combines all theme styles for optimal performance:
 * - Base Tailwind CSS
 * - Enhanced Components
 * - Mobile Optimizations
 * - Performance Enhancements
 *
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   ENHANCED MEGA MENU STYLES
   ========================================================================== */

/* ==========================================================================
   MEGA MENU CRITICAL FIXES - MERGED
   ========================================================================== */

/* Navigation Container */
.desktop-navigation {
    position: relative;
    z-index: 40;
}

.nav-menu {
    position: relative;
    z-index: 40;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

.nav-item.group {
    position: relative;
}

/* Mega Menu Container - Full Width with Critical Fixes */
.mega-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border-top: 4px solid #FCD34D !important;
    border-radius: 0 0 1.5rem 1.5rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 50 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    max-height: 0 !important;
}

/* Hover States - Multiple Selectors for Maximum Compatibility */
.nav-item:hover .mega-menu,
.nav-item.group:hover .mega-menu,
.group:hover .mega-menu,
li.group:hover .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Keep mega menu open when hovering over it */
.mega-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Navigation Link Hover Indicator */
.nav-item:hover > .nav-link,
.nav-item.group:hover > .nav-link {
    color: #1E40AF !important;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2)) !important;
}

/* Dropdown Arrow Animation */
.nav-item:hover .nav-link svg,
.nav-item.group:hover .nav-link svg {
    transform: rotate(180deg) !important;
}

/* ==========================================================================
   GENERAL THEME STYLES (NO MEGA MENU)
   ========================================================================== */



/* Item Badges */
.menu-item-enhanced .item-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2));
    color: #92400E;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.menu-item-enhanced a:hover .item-badge {
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.2), rgba(252, 211, 77, 0.3));
    transform: scale(1.05);
}

/* Arrow Indicators */
.menu-item-enhanced .arrow-indicator {
    margin-left: auto;
    margin-right: 0.5rem;
    opacity: 0;
    transform: translateX(0.5rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-enhanced a:hover .arrow-indicator {
    opacity: 1;
    transform: translateX(0);
}



/* ==========================================================================
   MODERN COUPON CARD STYLES - SIMPLIFIED
   ========================================================================== */

/* Base Coupon Card */
.coupon-card {
    min-height: 200px;
}

.coupon-card:hover {
    transform: translateY(-1px);
}

/* Featured Coupon Styling */
.coupon-card.featured {
    border-color: rgb(253 230 138) !important;
    background: linear-gradient(to bottom right, rgb(254 252 232), rgb(255 255 255)) !important;
    ring-width: 1px;
    ring-color: rgb(254 243 199);
}

/* Coupon Button Gradients */
.coupon-button.btn-code {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.coupon-button.btn-sale {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.coupon-button.btn-print {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}





/* ==========================================================================
   MODERN CREATIVE STORE CARD WITH ANIMATED BACKGROUND
   Branded Colors: Yellow (#FCD34D) & Dark Blue (#1E40AF)
   ========================================================================== */

/* Base Store Card */
.ag-store-card {
    position: relative;
    background: #ffffff;
    border-radius: 24px;
    padding: 20px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    overflow: visible;
    cursor: pointer;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Animated Background Elements */
.ag-store-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(30, 64, 175, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(252, 211, 77, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(30, 64, 175, 0.04) 0%, transparent 30%);
    opacity: 0;
    transition: all 0.8s ease;
    animation: float 6s ease-in-out infinite;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

.ag-store-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(252, 211, 77, 0.04) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(30, 64, 175, 0.04) 50%, transparent 70%);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

/* Hover Effects */
.ag-store-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(30, 64, 175, 0.15),
        0 15px 35px rgba(252, 211, 77, 0.1);
    border-color: rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover::before {
    opacity: 1;
    animation: float 3s ease-in-out infinite;
}

.ag-store-card:hover::after {
    opacity: 1;
    animation: shimmer 2s ease-in-out infinite;
}

/* Featured Store Card */
.ag-store-card.featured {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 2px solid #FCD34D;
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.2);
}

.ag-store-card.featured::before {
    background:
        radial-gradient(circle at 20% 20%, rgba(252, 211, 77, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(30, 64, 175, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(252, 211, 77, 0.08) 0%, transparent 30%);
}

.ag-store-card.featured:hover {
    transform: translateY(-16px) scale(1.03);
    box-shadow:
        0 30px 60px rgba(252, 211, 77, 0.25),
        0 20px 40px rgba(30, 64, 175, 0.15);
}

/* Store Logo - Rounded Style */
.ag-store-card .ag-store-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 16px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e5e7eb;
    transition: all 0.4s ease;
    z-index: 2;
    flex-shrink: 0;
}

.ag-store-card.featured .ag-store-logo {
    border-color: #FCD34D;
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card .ag-store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.ag-store-card:hover .ag-store-logo {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
}

.ag-store-card.featured:hover .ag-store-logo {
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover .ag-store-logo img {
    transform: scale(1.05);
}

/* Store Logo Placeholder */
.ag-store-card .ag-store-logo .ag-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    text-transform: uppercase;
}

.ag-store-card.featured .ag-store-logo .ag-logo-placeholder {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

/* Store Content */
.ag-store-card .ag-store-content {
    text-align: center;
    position: relative;
    z-index: 2;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Store Name */
.ag-store-card .ag-store-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 6px;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.ag-store-card .ag-store-name a {
    text-decoration: none;
    color: inherit;
    position: relative;
}

.ag-store-card:hover .ag-store-name a {
    color: #1E40AF;
}

.ag-store-card.featured .ag-store-name a {
    color: #1E40AF;
}

/* Coupon Count */
.ag-store-card .ag-coupons-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #6b7280;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
}

.ag-store-card .ag-coupons-count .ag-icon-wrapper {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    transform: scale(1.1);
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper svg {
    color: white;
}

.ag-store-card.featured .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card.featured:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
}

/* Action Buttons */
.ag-store-card .ag-store-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: auto;
}

.ag-store-card .ag-btn-primary {
    flex: 1;
    max-width: 160px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ag-store-card .ag-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.ag-store-card:hover .ag-btn-primary::before {
    left: 100%;
}

.ag-store-card .ag-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.4);
}

.ag-store-card.featured .ag-btn-primary {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

.ag-store-card.featured .ag-btn-primary:hover {
    box-shadow: 0 8px 20px rgba(252, 211, 77, 0.4);
}

.ag-store-card .ag-btn-secondary {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #6b7280;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.ag-store-card .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    border-color: #1E40AF;
    color: white;
    transform: translateY(-2px) scale(1.05);
}

.ag-store-card.featured .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    border-color: #FCD34D;
    color: #1E40AF;
}

/* Store Badge */
.ag-store-card .ag-store-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    animation: pulse-badge 2s infinite;
}

.ag-store-card.featured .ag-store-badge {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
    box-shadow: 0 4px 12px rgba(252, 211, 77, 0.4);
}

/* ==========================================================================
   ENHANCED COUPON CARD WITH SCRATCH-TO-REVEAL EFFECT
   Modern design with comprehensive meta field integration
   ========================================================================== */

/* Base Enhanced Coupon Card */
.ag-coupon-card {
    position: relative;
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #f3f4f6;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    cursor: pointer;
    min-height: 320px;
    height: 100%; /* Equal height in grid */
    display: flex;
    flex-direction: column;
}

/* Card content wrapper for flex layout */
.ag-coupon-card .relative.z-10 {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Push action section to bottom */
.ag-coupon-actions {
    margin-top: auto;
}

/* Hover Effects */
.ag-coupon-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #FCD34D;
}

/* Featured Coupon Card */
.ag-coupon-card.ag-coupon-featured {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 2px solid #FCD34D;
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.2);
}

.ag-coupon-card.ag-coupon-featured:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 25px 50px rgba(252, 211, 77, 0.3);
}

/* Scratch Effect Coupon Card */
.ag-coupon-card.ag-coupon-scratch {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 2px solid #e2e8f0;
}

.ag-coupon-card.ag-coupon-scratch:hover {
    border-color: #FCD34D;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

/* Minimal Coupon Card */
.ag-coupon-card.ag-coupon-minimal {
    border: 1px solid #e5e7eb;
    box-shadow: none;
}

.ag-coupon-card.ag-coupon-minimal:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Exclusive Badge Styling */
.ag-coupon-card.ag-coupon-exclusive::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 50px 50px 0;
    border-color: transparent #FCD34D transparent transparent;
    z-index: 5;
}

.ag-coupon-card.ag-coupon-exclusive::after {
    content: '★';
    position: absolute;
    top: 8px;
    right: 8px;
    color: #1E40AF;
    font-size: 14px;
    font-weight: bold;
    z-index: 6;
}

/* Animated Background Elements */
.ag-coupon-bg {
    pointer-events: none;
    z-index: 1;
}

/* Store Logo Small */
.ag-store-logo-small {
    transition: all 0.3s ease;
}

.ag-coupon-card:hover .ag-store-logo-small {
    transform: scale(1.1) rotate(5deg);
}

/* Badges */
.ag-badge-exclusive,
.ag-badge-shipping,
.ag-badge-save {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Coupon Title */
.ag-coupon-title {
    transition: all 0.3s ease;
}

.ag-coupon-title-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.ag-coupon-title-link:hover {
    color: #1E40AF;
}

/* Coupon Description */
.ag-coupon-description {
    line-height: 1.5;
}

/* Coupon Image */
.ag-coupon-image img {
    transition: transform 0.5s ease;
}

/* Voting Section */
.ag-coupon-voting button {
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.ag-coupon-voting button:hover {
    background-color: #f3f4f6;
    transform: scale(1.1);
}

/* Scratch-to-Reveal Effect */
.ag-scratch-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.ag-scratch-button {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.ag-scratch-button:hover {
    transform: scale(1.05);
}

.ag-scratch-button.scratching {
    animation: scratch-reveal 0.8s ease-out forwards;
}

@keyframes scratch-reveal {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1) rotateY(90deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1) rotateY(180deg);
        opacity: 0;
    }
}

.ag-coupon-code-hidden.revealed {
    opacity: 1;
    z-index: 3;
    animation: code-reveal 0.8s ease-out 0.4s forwards;
}

@keyframes code-reveal {
    0% {
        transform: scale(0.8) rotateY(-180deg);
        opacity: 0;
    }
    100% {
        transform: scale(1) rotateY(0deg);
        opacity: 1;
    }
}

/* Coupon Code Display Button (scratch coupon style) */
.ag-coupon-code-display {
    background: linear-gradient(135deg, #FCD34D, #F59E0B);
    border: 2px dashed #1E40AF;
    color: #1E40AF;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 48px;
}

.ag-coupon-code-display:hover {
    background: linear-gradient(135deg, #F59E0B, #D97706);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
}

/* Scratch effect overlay */
.ag-coupon-code-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(30, 64, 175, 0.1) 25%,
        transparent 25%,
        transparent 50%,
        rgba(30, 64, 175, 0.1) 50%,
        rgba(30, 64, 175, 0.1) 75%,
        transparent 75%
    );
    background-size: 8px 8px;
    pointer-events: none;
}

.ag-coupon-code-display .coupon-code-text {
    font-size: 16px;
    font-weight: 900;
    letter-spacing: 2px;
    position: relative;
    z-index: 2;
    /* Fallback color for browsers that don't support gradient text */
    color: #1E40AF;
    /* Partially hidden effect */
    background: linear-gradient(90deg,
        #1E40AF 0%,
        #1E40AF 30%,
        rgba(30, 64, 175, 0.3) 50%,
        rgba(30, 64, 175, 0.1) 70%,
        transparent 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Fallback for browsers that don't support background-clip: text */
@supports not (-webkit-background-clip: text) {
    .ag-coupon-code-display .coupon-code-text {
        color: #1E40AF;
        opacity: 0.8;
    }

    .ag-coupon-code-display:hover .coupon-code-text {
        opacity: 1;
    }
}

.ag-coupon-code-display .coupon-action-text {
    font-size: 11px;
    font-weight: 600;
    opacity: 0.9;
    position: relative;
    z-index: 2;
    color: #1E40AF;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Hover effect reveals more of the code */
.ag-coupon-code-display:hover .coupon-code-text {
    background: linear-gradient(90deg,
        #1E40AF 0%,
        #1E40AF 60%,
        rgba(30, 64, 175, 0.7) 80%,
        rgba(30, 64, 175, 0.3) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Deal Button */
.ag-deal-button {
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 48px;
    cursor: pointer;
}

.ag-deal-button:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Regular Coupon Button (fallback) */
.ag-coupon-button {
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ag-coupon-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ag-coupon-button:hover::before {
    left: 100%;
}

/* Expiration Info */
.ag-coupon-expiry {
    font-size: 11px;
}

/* Expired Coupon Styling */
.ag-coupon-card.ag-coupon-expired {
    filter: grayscale(50%);
}

.ag-coupon-card.ag-coupon-expired .ag-coupon-title {
    text-decoration: line-through;
}

.ag-coupon-card.ag-coupon-expired .ag-coupon-button {
    background: #9ca3af !important;
    cursor: not-allowed;
}

/* Load More Button */
.ag-load-more-coupons {
    position: relative;
    overflow: hidden;
}

.ag-load-more-coupons::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ag-load-more-coupons:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ag-coupon-card {
        min-height: 240px;
    }

    .ag-coupon-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .ag-coupon-title {
        font-size: 1rem;
    }

    .ag-coupon-description {
        font-size: 0.875rem;
    }
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ==========================================================================
   COUPON VOTING AND REVEAL CONTENT STYLES
   ========================================================================== */

/* Reveal Content */
.reveal-content {
    display: none;
}

.reveal-content.active {
    display: block !important;
}

/* Coupon Voting */
.coupon-vote.active {
    color: #10b981 !important;
    background-color: #d1fae5 !important;
}

.coupon-vote[data-vote-type="down"].active {
    color: #ef4444 !important;
    background-color: #fee2e2 !important;
}

.coupon-vote-wrapper.voted .coupon-vote:not(.active) {
    opacity: 0.5;
    pointer-events: none;
}

/* ==========================================================================
   READ MORE FUNCTIONALITY STYLES
   ========================================================================== */

/* Store Content with Read More */
.store-content {
    max-height: 8rem; /* 32 * 0.25rem = 8rem (128px) */
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.store-content.expanded {
    max-height: none;
}

/* Fade effect for collapsed content */
.store-content:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rem;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

/* Read More Button */
.read-more-btn {
    color: #0d9488 !important; /* secondary-600 */
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    background: transparent;
    padding: 0;
    text-decoration: underline;
    text-underline-offset: 2px;
    text-decoration-thickness: 1px;
}

.read-more-btn:hover {
    color: #0f766e !important; /* secondary-700 */
    text-decoration: none;
    transform: translateX(2px);
}

.read-more-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(13, 148, 136, 0.3);
    border-radius: 0.25rem;
}

/* ==========================================================================
   END OF THEME STYLES
   ========================================================================== */
