/*
 * Ag-Coupon Theme - Consolidated Styles
 *
 * This file combines all theme styles for optimal performance:
 * - Base Tailwind CSS
 * - Enhanced Components
 * - Mobile Optimizations
 * - Performance Enhancements
 *
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   ENHANCED MEGA MENU STYLES
   ========================================================================== */

/* ==========================================================================
   MEGA MENU CRITICAL FIXES - MERGED
   ========================================================================== */

/* Navigation Container */
.desktop-navigation {
    position: relative;
    z-index: 40;
}

.nav-menu {
    position: relative;
    z-index: 40;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

.nav-item.group {
    position: relative;
}

/* Mega Menu Container - Full Width with Critical Fixes */
.mega-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border-top: 4px solid #FCD34D !important;
    border-radius: 0 0 1.5rem 1.5rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 50 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    max-height: 0 !important;
}

/* Hover States - Multiple Selectors for Maximum Compatibility */
.nav-item:hover .mega-menu,
.nav-item.group:hover .mega-menu,
.group:hover .mega-menu,
li.group:hover .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Keep mega menu open when hovering over it */
.mega-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Navigation Link Hover Indicator */
.nav-item:hover > .nav-link,
.nav-item.group:hover > .nav-link {
    color: #1E40AF !important;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2)) !important;
}

/* Dropdown Arrow Animation */
.nav-item:hover .nav-link svg,
.nav-item.group:hover .nav-link svg {
    transform: rotate(180deg) !important;
}

/* ==========================================================================
   GENERAL THEME STYLES (NO MEGA MENU)
   ========================================================================== */



/* Item Badges */
.menu-item-enhanced .item-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2));
    color: #92400E;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.menu-item-enhanced a:hover .item-badge {
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.2), rgba(252, 211, 77, 0.3));
    transform: scale(1.05);
}

/* Arrow Indicators */
.menu-item-enhanced .arrow-indicator {
    margin-left: auto;
    margin-right: 0.5rem;
    opacity: 0;
    transform: translateX(0.5rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-enhanced a:hover .arrow-indicator {
    opacity: 1;
    transform: translateX(0);
}



/* ==========================================================================
   MODERN COUPON CARD STYLES - SIMPLIFIED
   ========================================================================== */

/* Base Coupon Card */
.coupon-card {
    min-height: 200px;
}

.coupon-card:hover {
    transform: translateY(-1px);
}

/* Featured Coupon Styling */
.coupon-card.featured {
    border-color: rgb(253 230 138) !important;
    background: linear-gradient(to bottom right, rgb(254 252 232), rgb(255 255 255)) !important;
    ring-width: 1px;
    ring-color: rgb(254 243 199);
}

/* Coupon Button Gradients */
.coupon-button.btn-code {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.coupon-button.btn-sale {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.coupon-button.btn-print {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}





/* ==========================================================================
   MODERN CREATIVE STORE CARD WITH ANIMATED BACKGROUND
   Branded Colors: Yellow (#FCD34D) & Dark Blue (#1E40AF)
   ========================================================================== */

/* Base Store Card */
.ag-store-card {
    position: relative;
    background: #ffffff;
    border-radius: 24px;
    padding: 20px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    overflow: visible;
    cursor: pointer;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Animated Background Elements */
.ag-store-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(30, 64, 175, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(252, 211, 77, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(30, 64, 175, 0.04) 0%, transparent 30%);
    opacity: 0;
    transition: all 0.8s ease;
    animation: float 6s ease-in-out infinite;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

.ag-store-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(252, 211, 77, 0.04) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(30, 64, 175, 0.04) 50%, transparent 70%);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 1;
    border-radius: 24px;
    pointer-events: none;
}

/* Hover Effects */
.ag-store-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(30, 64, 175, 0.15),
        0 15px 35px rgba(252, 211, 77, 0.1);
    border-color: rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover::before {
    opacity: 1;
    animation: float 3s ease-in-out infinite;
}

.ag-store-card:hover::after {
    opacity: 1;
    animation: shimmer 2s ease-in-out infinite;
}

/* Featured Store Card */
.ag-store-card.featured {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 2px solid #FCD34D;
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.2);
}

.ag-store-card.featured::before {
    background:
        radial-gradient(circle at 20% 20%, rgba(252, 211, 77, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(30, 64, 175, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(252, 211, 77, 0.08) 0%, transparent 30%);
}

.ag-store-card.featured:hover {
    transform: translateY(-16px) scale(1.03);
    box-shadow:
        0 30px 60px rgba(252, 211, 77, 0.25),
        0 20px 40px rgba(30, 64, 175, 0.15);
}

/* Store Logo - Rounded Style */
.ag-store-card .ag-store-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 16px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e5e7eb;
    transition: all 0.4s ease;
    z-index: 2;
    flex-shrink: 0;
}

.ag-store-card.featured .ag-store-logo {
    border-color: #FCD34D;
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card .ag-store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.ag-store-card:hover .ag-store-logo {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
}

.ag-store-card.featured:hover .ag-store-logo {
    box-shadow: 0 8px 25px rgba(252, 211, 77, 0.3);
}

.ag-store-card:hover .ag-store-logo img {
    transform: scale(1.05);
}

/* Store Logo Placeholder */
.ag-store-card .ag-store-logo .ag-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    text-transform: uppercase;
}

.ag-store-card.featured .ag-store-logo .ag-logo-placeholder {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

/* Store Content */
.ag-store-card .ag-store-content {
    text-align: center;
    position: relative;
    z-index: 2;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Store Name */
.ag-store-card .ag-store-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 6px;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.ag-store-card .ag-store-name a {
    text-decoration: none;
    color: inherit;
    position: relative;
}

.ag-store-card:hover .ag-store-name a {
    color: #1E40AF;
}

.ag-store-card.featured .ag-store-name a {
    color: #1E40AF;
}

/* Coupon Count */
.ag-store-card .ag-coupons-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #6b7280;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 12px;
}

.ag-store-card .ag-coupons-count .ag-icon-wrapper {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    transform: scale(1.1);
}

.ag-store-card:hover .ag-coupons-count .ag-icon-wrapper svg {
    color: white;
}

.ag-store-card.featured .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #fef3c7, #FCD34D);
}

.ag-store-card.featured:hover .ag-coupons-count .ag-icon-wrapper {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
}

/* Action Buttons */
.ag-store-card .ag-store-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: auto;
}

.ag-store-card .ag-btn-primary {
    flex: 1;
    max-width: 160px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ag-store-card .ag-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.ag-store-card:hover .ag-btn-primary::before {
    left: 100%;
}

.ag-store-card .ag-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.4);
}

.ag-store-card.featured .ag-btn-primary {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
}

.ag-store-card.featured .ag-btn-primary:hover {
    box-shadow: 0 8px 20px rgba(252, 211, 77, 0.4);
}

.ag-store-card .ag-btn-secondary {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #6b7280;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.ag-store-card .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    border-color: #1E40AF;
    color: white;
    transform: translateY(-2px) scale(1.05);
}

.ag-store-card.featured .ag-btn-secondary:hover {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    border-color: #FCD34D;
    color: #1E40AF;
}

/* Store Badge */
.ag-store-card .ag-store-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: linear-gradient(135deg, #1E40AF, #3b82f6);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    animation: pulse-badge 2s infinite;
}

.ag-store-card.featured .ag-store-badge {
    background: linear-gradient(135deg, #FCD34D, #f59e0b);
    color: #1E40AF;
    box-shadow: 0 4px 12px rgba(252, 211, 77, 0.4);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ==========================================================================
   COUPON VOTING AND REVEAL CONTENT STYLES
   ========================================================================== */

/* Reveal Content */
.reveal-content {
    display: none;
}

.reveal-content.active {
    display: block !important;
}

/* Coupon Voting */
.coupon-vote.active {
    color: #10b981 !important;
    background-color: #d1fae5 !important;
}

.coupon-vote[data-vote-type="down"].active {
    color: #ef4444 !important;
    background-color: #fee2e2 !important;
}

.coupon-vote-wrapper.voted .coupon-vote:not(.active) {
    opacity: 0.5;
    pointer-events: none;
}

/* ==========================================================================
   READ MORE FUNCTIONALITY STYLES
   ========================================================================== */

/* Store Content with Read More */
.store-content {
    max-height: 8rem; /* 32 * 0.25rem = 8rem (128px) */
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.store-content.expanded {
    max-height: none;
}

/* Fade effect for collapsed content */
.store-content:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rem;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

/* Read More Button */
.read-more-btn {
    color: #0d9488 !important; /* secondary-600 */
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    background: transparent;
    padding: 0;
    text-decoration: underline;
    text-underline-offset: 2px;
    text-decoration-thickness: 1px;
}

.read-more-btn:hover {
    color: #0f766e !important; /* secondary-700 */
    text-decoration: none;
    transform: translateX(2px);
}

.read-more-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(13, 148, 136, 0.3);
    border-radius: 0.25rem;
}

/* ==========================================================================
   END OF THEME STYLES
   ========================================================================== */
