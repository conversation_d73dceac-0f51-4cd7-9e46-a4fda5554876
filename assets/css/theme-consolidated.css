/*
 * Ag-Coupon Theme - Consolidated Styles
 *
 * This file combines all theme styles for optimal performance:
 * - Base Tailwind CSS
 * - Enhanced Components
 * - Mobile Optimizations
 * - Performance Enhancements
 *
 * Color Scheme: Yellow (#FCD34D) & Dark Blue (#1E40AF)
 */

/* ==========================================================================
   ENHANCED MEGA MENU STYLES
   ========================================================================== */

/* ==========================================================================
   MEGA MENU CRITICAL FIXES - MERGED
   ========================================================================== */

/* Navigation Container */
.desktop-navigation {
    position: relative;
    z-index: 40;
}

.nav-menu {
    position: relative;
    z-index: 40;
}

/* Navigation Items */
.nav-item {
    position: relative;
}

.nav-item.group {
    position: relative;
}

/* Mega Menu Container - Full Width with Critical Fixes */
.mega-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    border-top: 4px solid #FCD34D !important;
    border-radius: 0 0 1.5rem 1.5rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 50 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    max-height: 0 !important;
}

/* Hover States - Multiple Selectors for Maximum Compatibility */
.nav-item:hover .mega-menu,
.nav-item.group:hover .mega-menu,
.group:hover .mega-menu,
li.group:hover .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Keep mega menu open when hovering over it */
.mega-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    max-height: 600px !important;
}

/* Navigation Link Hover Indicator */
.nav-item:hover > .nav-link,
.nav-item.group:hover > .nav-link {
    color: #1E40AF !important;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2)) !important;
}

/* Dropdown Arrow Animation */
.nav-item:hover .nav-link svg,
.nav-item.group:hover .nav-link svg {
    transform: rotate(180deg) !important;
}

/* ==========================================================================
   GENERAL THEME STYLES (NO MEGA MENU)
   ========================================================================== */



/* Item Badges */
.menu-item-enhanced .item-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.1), rgba(252, 211, 77, 0.2));
    color: #92400E;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.menu-item-enhanced a:hover .item-badge {
    background: linear-gradient(135deg, rgba(252, 211, 77, 0.2), rgba(252, 211, 77, 0.3));
    transform: scale(1.05);
}

/* Arrow Indicators */
.menu-item-enhanced .arrow-indicator {
    margin-left: auto;
    margin-right: 0.5rem;
    opacity: 0;
    transform: translateX(0.5rem);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-enhanced a:hover .arrow-indicator {
    opacity: 1;
    transform: translateX(0);
}



/* ==========================================================================
   MODERN COUPON CARD STYLES - SIMPLIFIED
   ========================================================================== */

/* Base Coupon Card */
.coupon-card {
    min-height: 200px;
}

.coupon-card:hover {
    transform: translateY(-1px);
}

/* Featured Coupon Styling */
.coupon-card.featured {
    border-color: rgb(253 230 138) !important;
    background: linear-gradient(to bottom right, rgb(254 252 232), rgb(255 255 255)) !important;
    ring-width: 1px;
    ring-color: rgb(254 243 199);
}

/* Coupon Button Gradients */
.coupon-button.btn-code {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.coupon-button.btn-sale {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.coupon-button.btn-print {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ==========================================================================
   COUPON VOTING AND REVEAL CONTENT STYLES
   ========================================================================== */

/* Reveal Content */
.reveal-content {
    display: none;
}

.reveal-content.active {
    display: block !important;
}

/* Coupon Voting */
.coupon-vote.active {
    color: #10b981 !important;
    background-color: #d1fae5 !important;
}

.coupon-vote[data-vote-type="down"].active {
    color: #ef4444 !important;
    background-color: #fee2e2 !important;
}

.coupon-vote-wrapper.voted .coupon-vote:not(.active) {
    opacity: 0.5;
    pointer-events: none;
}

/* ==========================================================================
   READ MORE FUNCTIONALITY STYLES
   ========================================================================== */

/* Store Content with Read More */
.store-content {
    max-height: 8rem; /* 32 * 0.25rem = 8rem (128px) */
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.store-content.expanded {
    max-height: none;
}

/* Fade effect for collapsed content */
.store-content:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rem;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
}

/* Read More Button */
.read-more-btn {
    color: #0d9488 !important; /* secondary-600 */
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    background: transparent;
    padding: 0;
    text-decoration: underline;
    text-underline-offset: 2px;
    text-decoration-thickness: 1px;
}

.read-more-btn:hover {
    color: #0f766e !important; /* secondary-700 */
    text-decoration: none;
    transform: translateX(2px);
}

.read-more-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(13, 148, 136, 0.3);
    border-radius: 0.25rem;
}

/* ==========================================================================
   END OF THEME STYLES
   ========================================================================== */
