<?php
/**
 * Debug Load More Functionality
 * Access via: http://l-couponatt.local/wp-content/themes/Ag-Coupon/debug-load-more.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Load More - Ag-Coupon</title>
    
    <!-- Load theme styles -->
    <?php wp_head(); ?>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        .load-more-test {
            border: 2px dashed #007cba;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Load More Debug Tool</h1>
        <p>This page will help debug the AJAX load more functionality.</p>
        
        <button onclick="runFullDebug()">🔍 Run Full Debug</button>
        <button onclick="testAjaxDirect()">🧪 Test AJAX Direct</button>
        <button onclick="testLoadMoreClick()">🖱️ Test Load More Click</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="debug-results"></div>
    </div>

    <div class="debug-container">
        <h2>🎯 Live Load More Test</h2>
        <p>This section simulates the actual home page load more functionality:</p>
        
        <div class="load-more-test">
            <!-- Simulate the home page coupon grid -->
            <div class="latest-coupons-grid-container mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="latest-coupons-grid">
                    <?php
                    // Get some test coupons
                    $test_coupons = get_posts(array(
                        'post_type' => 'coupon',
                        'posts_per_page' => 4,
                        'post_status' => 'publish'
                    ));
                    
                    foreach ($test_coupons as $coupon) {
                        echo '<div class="coupon-grid-item bg-white p-4 rounded border">';
                        echo '<h3>' . get_the_title($coupon->ID) . '</h3>';
                        echo '<p>ID: ' . $coupon->ID . '</p>';
                        echo '</div>';
                    }
                    ?>
                </div>

                <!-- Load More Button - Exact copy from render_coupons_grid -->
                <div class="load-more-container mt-12 mb-8">
                    <div class="flex justify-center">
                        <div class="load-more">
                            <button type="button"
                                   class="load-more-btn inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                   data-doing="load_coupons"
                                   data-next-page="2"
                                   data-loading-text="جاري التحميل..."
                                   data-per-page="4"
                                   data-args='{"layout":"","posts_per_page":"","num_words":"","hide_expired":""}'>
                                
                                <!-- Button Text -->
                                <span class="btn-text">تحميل المزيد من الكوبونات</span>
                                
                                <!-- Loading Spinner -->
                                <span class="loading-spinner hidden ml-2">
                                    <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                
                                <!-- Button Icon -->
                                <span class="btn-icon ml-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php wp_footer(); ?>

    <script>
        let debugOutput = '';
        
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugOutput += `[${timestamp}] ${message}\n`;
            
            const resultsDiv = document.getElementById('debug-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `debug-result ${type}`;
            resultDiv.textContent = `[${timestamp}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            // Auto-scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function clearResults() {
            document.getElementById('debug-results').innerHTML = '';
            debugOutput = '';
            addResult('Debug results cleared', 'info');
        }
        
        function runFullDebug() {
            addResult('=== STARTING FULL DEBUG ===', 'info');
            
            // Check WordPress and theme
            addResult('WordPress loaded: ' + (typeof wp !== 'undefined' ? 'Yes' : 'No'), 'info');
            addResult('jQuery loaded: ' + (typeof $ !== 'undefined' ? 'Yes' : 'No'), 'info');
            
            // Check ST object
            if (typeof ST !== 'undefined') {
                addResult('ST object found', 'success');
                addResult('AJAX URL: ' + ST.ajax_url, 'info');
                addResult('Nonce: ' + ST._wpnonce, 'info');
            } else {
                addResult('ST object NOT found', 'error');
            }
            
            // Check AgCoupon object
            if (typeof window.AgCoupon !== 'undefined') {
                addResult('AgCoupon object found', 'success');
                addResult('AgCoupon initialized: ' + (window.AgCoupon.initialized ? 'Yes' : 'No'), 'info');
            } else {
                addResult('AgCoupon object NOT found', 'error');
            }
            
            // Check for load more buttons
            const $buttons = $('.load-more-btn, .ag-load-more-coupons');
            addResult('Load more buttons found: ' + $buttons.length, $buttons.length > 0 ? 'success' : 'error');
            
            $buttons.each(function(i) {
                const $btn = $(this);
                addResult(`Button ${i+1}: ${$btn.text().trim()}`, 'info');
                addResult(`Button ${i+1} data: ${JSON.stringify($btn.data())}`, 'info');
            });
            
            // Check target container
            const $container = $('#latest-coupons-grid');
            addResult('Target container found: ' + ($container.length > 0 ? 'Yes' : 'No'), $container.length > 0 ? 'success' : 'error');
            addResult('Container children: ' + $container.children().length, 'info');
            
            // Check if debug functions are available
            if (typeof window.debugLoadMore === 'function') {
                addResult('debugLoadMore function available', 'success');
                window.debugLoadMore();
            } else {
                addResult('debugLoadMore function NOT available', 'error');
            }
            
            addResult('=== FULL DEBUG COMPLETE ===', 'info');
        }
        
        function testAjaxDirect() {
            addResult('Testing AJAX directly...', 'info');
            
            if (typeof ST === 'undefined') {
                addResult('Cannot test AJAX: ST object not found', 'error');
                return;
            }
            
            const testData = {
                action: 'wpcoupon_coupon_ajax',
                st_doing: 'load_coupons',
                next_page: 2,
                _wpnonce: ST._wpnonce,
                args: {
                    layout: '',
                    posts_per_page: '4',
                    num_words: '',
                    hide_expired: ''
                }
            };
            
            addResult('AJAX data: ' + JSON.stringify(testData), 'info');
            
            $.ajax({
                url: ST.ajax_url,
                type: 'POST',
                data: testData,
                dataType: 'json',
                success: function(response) {
                    addResult('AJAX Success!', 'success');
                    addResult('Response: ' + JSON.stringify(response), 'success');
                    
                    if (response.success && response.data && response.data.content) {
                        addResult('Content length: ' + response.data.content.length + ' characters', 'success');
                        addResult('Next page: ' + response.data.next_page, 'info');
                        addResult('Max pages: ' + response.data.max_pages, 'info');
                    }
                },
                error: function(xhr, status, error) {
                    addResult('AJAX Error: ' + error, 'error');
                    addResult('Status: ' + status, 'error');
                    addResult('Response: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function testLoadMoreClick() {
            addResult('Testing load more button click...', 'info');
            
            const $button = $('.load-more-btn').first();
            if ($button.length === 0) {
                addResult('No load more button found', 'error');
                return;
            }
            
            addResult('Clicking load more button...', 'info');
            $button.trigger('click');
        }
        
        // Auto-run basic debug when page loads
        $(document).ready(function() {
            setTimeout(function() {
                addResult('Page loaded, running initial debug...', 'info');
                runFullDebug();
            }, 1000);
        });
    </script>
</body>
</html>
