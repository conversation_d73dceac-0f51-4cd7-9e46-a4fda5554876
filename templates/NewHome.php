<?php
/**
 * Template Name: Ag-Coupon Modern Home
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

get_header();

// Initialize simple store tracking for home page

// Get data for the home page sections
$featured_stores = wpcoupon_get_featured_stores(12);
$latest_coupons = wpcoupon_get_coupons(array('posts_per_page' => 8, 'hide_expired' => true));
$categories = get_terms(array('taxonomy' => 'coupon_category', 'hide_empty' => true, 'number' => 8));

// Note: Popular stores will be automatically filtered to exclude featured stores
// using the comprehensive store tracking system in wpcoupon_render_store_card()

// Make data available to template parts
set_query_var('featured_stores', $featured_stores);
set_query_var('latest_coupons', $latest_coupons);
set_query_var('categories', $categories);

?>

<!-- Modern Home Page Container -->
<div id="home-page" class="ag-coupon-home">

    <?php
    /**
     * Hero Section
     * Displays main hero banner with statistics and floating cards
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Featured Stores Section
     * Displays featured/promoted stores with special badges
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Latest Coupons Section
     * Displays the most recent coupons with interactive buttons
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Categories Section
     * Displays coupon categories for easy browsing
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Popular Stores Section
     * Displays most popular stores based on user activity
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Newsletter Section
     * Displays newsletter subscription form with AJAX functionality
     */
    get_template_part('template-parts/home/<USER>');
    ?>

</div>

<?php
// Simple debug information for the home page
if (WP_DEBUG && current_user_can('manage_options')) {
    echo '<!-- Home Page Debug: Featured Stores: ' . count($featured_stores) . ' -->';
}
?>

<?php get_footer(); ?>