<?php
/**
 * Template Name: Ag-Coupon Modern Home
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

get_header();

// Initialize simple store tracking for home page

// Initialize both store and coupon tracking for this page
ag_reset_store_tracking();
ag_reset_coupon_tracking();

// Get data for the home page sections with enhanced deduplication
$featured_stores = get_featured_stores(
    12,
    false, // Don't exclude displayed yet (this is the first section)
    'home-featured'
);

// Get featured coupons with deduplication
$featured_coupons = get_featured_coupons(
    8,
    false, // Don't exclude displayed yet (this is the first section)
    'home-featured-coupons'
);

// Get latest coupons (will automatically exclude featured coupons)
$latest_coupons = get_all_coupons(
    12,
    array(), // no manual exclusions
    true, // exclude already displayed
    'home-latest-coupons'
);

$categories = get_terms(array('taxonomy' => 'coupon_category', 'hide_empty' => true, 'number' => 8));

// Note: Both stores and coupons will be automatically filtered to prevent duplicates
// using the comprehensive tracking systems

// Make data available to template parts
set_query_var('featured_stores', $featured_stores);
set_query_var('featured_coupons', $featured_coupons);
set_query_var('latest_coupons', $latest_coupons);
set_query_var('categories', $categories);

?>

<!-- Modern Home Page Container -->
<div id="home-page" class="ag-coupon-home">

    <?php
    /**
     * Hero Section
     * Displays main hero banner with statistics and floating cards
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Featured Stores Section
     * Displays featured/promoted stores with special badges
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Featured Coupons Section
     * Displays exclusive and premium coupons with enhanced cards
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Latest Coupons Section
     * Displays the most recent coupons with interactive buttons
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Categories Section
     * Displays coupon categories for easy browsing
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Popular Stores Section
     * Displays most popular stores based on user activity
     */
    get_template_part('template-parts/home/<USER>');
    ?>

    <?php
    /**
     * Newsletter Section
     * Displays newsletter subscription form with AJAX functionality
     */
    get_template_part('template-parts/home/<USER>');
    ?>

</div>

<?php
// Simple debug information for the home page
if (WP_DEBUG && current_user_can('manage_options')) {
    echo '<!-- Home Page Debug: Featured Stores: ' . count($featured_stores) . ' -->';
}
?>

<?php get_footer(); ?>