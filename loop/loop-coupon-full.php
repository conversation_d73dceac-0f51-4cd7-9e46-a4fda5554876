<?php
/**
 * Enhanced AJAX loop template for store coupons (full layout)
 * Uses unified enhanced coupon card system
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Setup coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    wpcoupon_setup_coupon($post);
}

// Debug: Check if we're in AJAX context
if (defined('DOING_AJAX') && DOING_AJAX) {
    echo '<!-- Enhanced AJAX Store Loop: Processing coupon ID ' . $post->ID . ' -->';
}

// Use enhanced coupon card with proper grid wrapper
echo '<div class="coupon-grid-item">';
if (function_exists('render_enhanced_coupon_card')) {
    render_enhanced_coupon_card($post, 'featured', array(
        'section' => 'ajax-store-full',
        'track_display' => false,
        'strict_deduplication' => false,
        'force_render' => true,
        'show_store_logo' => true,
        'show_badges' => true,
        'show_description' => true,
        'description_length' => 20, // Longer description for full layout
        'scratch_effect' => false,
        'hover_effects' => true
    ));
} else {
    // Fallback to helper function
    wpcoupon_render_coupon_card($post, true);
}
echo '</div>';
