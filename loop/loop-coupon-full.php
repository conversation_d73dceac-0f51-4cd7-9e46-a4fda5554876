<?php
/**
 * Full layout loop template for AJAX loaded coupons
 * Used by wpcoupon_ajax_store_coupons function
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Setup coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    wpcoupon_setup_coupon($post);
}

// Use our enhanced coupon card component with featured style
wpcoupon_render_coupon_card($post, true);
