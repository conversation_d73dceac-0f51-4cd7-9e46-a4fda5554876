<?php
global $post, $authordata;

if ( ! is_object( $authordata ) ) {
    $authordata = get_user_by( 'ID', $post->post_author );
}

$is_sticky = is_sticky();
$post_categories = get_the_category();
$reading_time = wpcoupon_get_reading_time( get_the_content() );
?>
<!-- Tailwind CSS Blog Card -->
<article <?php post_class( 'blog-card bg-white rounded-xl shadow-coupon hover:shadow-coupon-hover transition-all duration-300 overflow-hidden group' ); ?>>

    <?php if ( $is_sticky ) { ?>
        <!-- Sticky Post Badge -->
        <div class="absolute top-4 right-4 z-10">
            <span class="bg-primary-300 text-primary-900 text-xs px-2 py-1 rounded-full font-semibold">
                <?php esc_html_e( 'مثبت', 'wp-coupon' ); ?>
            </span>
        </div>
    <?php } ?>

    <!-- Featured Image -->
    <?php if ( has_post_thumbnail() ) { ?>
        <div class="blog-card-image relative overflow-hidden">
            <a href="<?php the_permalink(); ?>" class="block">
                <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                    <?php the_post_thumbnail( 'wpcoupon_blog_medium', array( 'class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-300' ) ); ?>
                </div>
            </a>

            <!-- Category Badge -->
            <?php if ( $post_categories ) { ?>
                <div class="absolute bottom-4 right-4">
                    <a href="<?php echo get_category_link( $post_categories[0]->term_id ); ?>"
                       class="bg-secondary-500 text-white text-xs px-2 py-1 rounded-full font-semibold hover:bg-secondary-600 transition-colors duration-200">
                        <?php echo esc_html( $post_categories[0]->name ); ?>
                    </a>
                </div>
            <?php } ?>
        </div>
    <?php } ?>

    <!-- Blog Content -->
    <div class="blog-card-body p-6">
        <!-- Post Meta -->
        <?php if ( $authordata ) { ?>
        <div class="blog-meta flex items-center mb-4">
            <!-- Author Avatar -->
            <div class="author-avatar flex-shrink-0">
                <a href="<?php echo esc_url( get_author_posts_url( $authordata->ID, $authordata->user_nicename ) ); ?>"
                   class="block">
                    <div class="w-10 h-10 rounded-full overflow-hidden ring-2 ring-gray-100">
                        <?php echo get_avatar( get_the_author_meta( 'email', $authordata->ID ), 40, '', '', array( 'class' => 'w-full h-full object-cover' ) ); ?>
                    </div>
                </a>
            </div>

            <!-- Post Meta Data -->
            <div class="post-meta-data mr-3 flex-1">
                <div class="flex items-center text-sm text-gray-600 space-x-4 space-x-reverse">
                    <!-- Author Name -->
                    <?php if ( is_object( $authordata ) ) { ?>
                        <span class="author-name">
                            <a href="<?php echo esc_url( get_author_posts_url( $authordata->ID, $authordata->user_nicename ) ); ?>"
                               class="text-secondary-600 hover:text-secondary-700 font-medium transition-colors duration-200"
                               title="<?php echo esc_attr( sprintf( esc_html__( 'Posts by %s', 'wp-coupon' ), get_the_author() ) ); ?>">
                                <?php echo get_the_author(); ?>
                            </a>
                        </span>
                    <?php } ?>

                    <!-- Post Date -->
                    <span class="post-date flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo get_the_date(); ?>
                    </span>

                    <!-- Comments Count -->
                    <span class="comment-number flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                        </svg>
                        <?php comments_number(
                            esc_html__( '0', 'wp-coupon' ),
                            esc_html__( '1', 'wp-coupon' ),
                            esc_html__( '%', 'wp-coupon' )
                        ); ?>
                    </span>

                    <!-- Reading Time -->
                    <?php if ( $reading_time ) { ?>
                    <span class="reading-time flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo $reading_time; ?> <?php esc_html_e( 'دقيقة', 'wp-coupon' ); ?>
                    </span>
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php } ?>

        <!-- Post Title -->
        <h2 class="blog-title text-xl font-bold text-gray-900 mb-3 leading-tight">
            <a href="<?php the_permalink(); ?>"
               title="<?php echo esc_attr( get_the_title() ); ?>"
               class="text-gray-900 hover:text-secondary-600 transition-colors duration-200">
                <?php the_title(); ?>
            </a>
        </h2>

        <!-- Post Excerpt -->
        <div class="blog-excerpt text-gray-600 text-sm leading-relaxed mb-4">
            <?php the_excerpt(); ?>
        </div>

        <!-- Read More Button -->
        <div class="blog-card-footer">
            <a href="<?php the_permalink(); ?>"
               class="btn-outline inline-flex items-center py-2 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-300">
                <?php esc_html_e( 'إقرأ المزيد', 'wp-coupon' ); ?>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</article>
