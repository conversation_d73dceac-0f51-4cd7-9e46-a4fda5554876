<?php
/**
 * Default loop template for AJAX loaded coupons
 * Used by wpcoupon_ajax_coupons function
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Setup coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    wpcoupon_setup_coupon($post);
}

// Debug: Check if we're in AJAX context
if (defined('DOING_AJAX') && DOING_AJAX) {
    echo '<!-- AJAX Loop: Processing coupon ID ' . $post->ID . ' - ' . get_the_title($post->ID) . ' -->';
}

// Use our enhanced coupon card component with proper grid wrapper
if (function_exists('render_enhanced_coupon_card')) {
    echo '<!-- Using enhanced coupon card for AJAX ID: ' . $post->ID . ' -->';
    echo '<div class="coupon-grid-item">';
    render_enhanced_coupon_card($post, 'default', array(
        'section' => 'ajax-load-more',
        'track_display' => false, // Don't track in AJAX loads
        'strict_deduplication' => false, // Bypass strict deduplication for AJAX
        'force_render' => true, // Force rendering even if already displayed
        'show_store_logo' => true,
        'show_badges' => true,
        'show_description' => true,
        'description_length' => 15,
        'scratch_effect' => false, // Disable scratch effect for consistency
        'hover_effects' => true
    ));
    echo '</div>';
} else {
    // Fallback to existing function
    echo '<!-- Using fallback render function for coupon ' . $post->ID . ' -->';
    echo '<div class="coupon-grid-item">';
    wpcoupon_render_coupon_card($post, false);
    echo '</div>';
}
