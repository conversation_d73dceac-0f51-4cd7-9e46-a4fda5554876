<?php
/**
 * Default loop template for AJAX loaded coupons
 * Used by wpcoupon_ajax_coupons function
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Setup coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    wpcoupon_setup_coupon($post);
}

// Use our enhanced coupon card component
if (function_exists('render_enhanced_coupon_card')) {
    render_enhanced_coupon_card($post, 'default', array(
        'section' => 'ajax-load-more',
        'track_display' => false // Don't track in AJAX loads
    ));
} else {
    // Fallback to existing function
    wpcoupon_render_coupon_card($post, false);
}
