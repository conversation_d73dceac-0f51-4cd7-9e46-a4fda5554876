<?php
/**
 * Check JavaScript Loading
 */

// Load WordPress
require_once('../../../wp-load.php');

// Simulate home page
$GLOBALS['wp_query']->is_home = true;
$GLOBALS['wp_query']->is_front_page = true;

// Get the theme
$theme = wp_get_theme();

echo "<h1>JavaScript Loading Check</h1>\n";

// Check if scripts are enqueued
echo "<h2>1. Enqueued Scripts</h2>\n";

// Simulate wp_enqueue_scripts action
do_action('wp_enqueue_scripts');

global $wp_scripts;
if ($wp_scripts) {
    echo "<ul>\n";
    foreach ($wp_scripts->registered as $handle => $script) {
        if (strpos($handle, 'wpcoupon') !== false || strpos($handle, 'ag-coupon') !== false) {
            $status = in_array($handle, $wp_scripts->queue) ? '✅ ENQUEUED' : '❌ NOT ENQUEUED';
            echo "<li><strong>$handle</strong>: $status<br>";
            echo "Source: " . $script->src . "<br>";
            echo "Dependencies: " . implode(', ', $script->deps) . "</li>\n";
        }
    }
    echo "</ul>\n";
}

// Check if files exist
echo "<h2>2. File Existence Check</h2>\n";
$js_files = array(
    'global.js' => get_template_directory() . '/assets/js/global.js',
    'ag-coupon-new.js' => get_template_directory() . '/assets/js/ag-coupon-new.js',
    'enhanced-coupon-interactions.js' => get_template_directory() . '/assets/js/enhanced-coupon-interactions.js',
    'theme.js' => get_template_directory() . '/assets/js/theme.js'
);

foreach ($js_files as $name => $path) {
    $exists = file_exists($path);
    $size = $exists ? filesize($path) : 0;
    echo "<p><strong>$name</strong>: " . ($exists ? "✅ EXISTS ($size bytes)" : "❌ NOT FOUND") . "</p>\n";
}

// Check localized data
echo "<h2>3. Localized Data</h2>\n";
echo "<script>\n";
echo "document.addEventListener('DOMContentLoaded', function() {\n";
echo "    console.log('=== JAVASCRIPT LOADING CHECK ===');\n";
echo "    console.log('jQuery loaded:', typeof $ !== 'undefined');\n";
echo "    console.log('ST object:', typeof ST !== 'undefined' ? ST : 'NOT FOUND');\n";
echo "    console.log('AgCoupon object:', typeof window.AgCoupon !== 'undefined' ? window.AgCoupon : 'NOT FOUND');\n";
echo "    \n";
echo "    // Check for load more buttons\n";
echo "    const buttons = document.querySelectorAll('.load-more-btn, .ag-load-more-coupons');\n";
echo "    console.log('Load more buttons found:', buttons.length);\n";
echo "    \n";
echo "    // Check for target container\n";
echo "    const container = document.getElementById('latest-coupons-grid');\n";
echo "    console.log('Target container found:', !!container);\n";
echo "    \n";
echo "    // Display results on page\n";
echo "    const resultDiv = document.getElementById('js-check-results');\n";
echo "    if (resultDiv) {\n";
echo "        resultDiv.innerHTML = \n";
echo "            '<p>jQuery: ' + (typeof $ !== 'undefined' ? '✅ Loaded' : '❌ Not loaded') + '</p>' +\n";
echo "            '<p>ST Object: ' + (typeof ST !== 'undefined' ? '✅ Found' : '❌ Not found') + '</p>' +\n";
echo "            '<p>AgCoupon: ' + (typeof window.AgCoupon !== 'undefined' ? '✅ Found' : '❌ Not found') + '</p>' +\n";
echo "            '<p>Load More Buttons: ' + buttons.length + '</p>' +\n";
echo "            '<p>Target Container: ' + (container ? '✅ Found' : '❌ Not found') + '</p>';\n";
echo "    }\n";
echo "});\n";
echo "</script>\n";

echo "<h2>4. Runtime Check</h2>\n";
echo "<div id='js-check-results'>Loading...</div>\n";

// Test load more button
echo "<h2>5. Test Load More Button</h2>\n";
echo "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>\n";
echo "<button class='load-more-btn' data-doing='load_coupons' data-next-page='2' onclick='testClick()'>Test Load More Button</button>\n";
echo "<div id='click-result'></div>\n";
echo "</div>\n";

echo "<script>\n";
echo "function testClick() {\n";
echo "    const resultDiv = document.getElementById('click-result');\n";
echo "    resultDiv.innerHTML = 'Button clicked! Check console for details.';\n";
echo "    console.log('Test button clicked!');\n";
echo "    \n";
echo "    // Try to trigger the actual load more functionality\n";
echo "    if (typeof window.debugLoadMore === 'function') {\n";
echo "        console.log('Calling debugLoadMore...');\n";
echo "        window.debugLoadMore();\n";
echo "    } else {\n";
echo "        console.log('debugLoadMore function not available');\n";
echo "    }\n";
echo "}\n";
echo "</script>\n";

// Include WordPress head to load scripts
wp_head();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.load-more-btn { 
    background: #007cba; 
    color: white; 
    border: none; 
    padding: 10px 20px; 
    border-radius: 4px; 
    cursor: pointer; 
}
</style>
