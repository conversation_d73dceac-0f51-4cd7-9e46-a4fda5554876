<?php
/**
 * Enhanced AJAX loop template for coupons
 * Uses unified enhanced coupon card system
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Setup coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    wpcoupon_setup_coupon($post);
}

// Use enhanced coupon card with proper grid wrapper
echo '<div class="coupon-grid-item">';
if (function_exists('render_enhanced_coupon_card')) {
    render_enhanced_coupon_card($post, 'default', array(
        'section' => 'ajax-default',
        'track_display' => false,
        'strict_deduplication' => false,
        'force_render' => true,
        'show_store_logo' => true,
        'show_badges' => true,
        'show_description' => true,
        'description_length' => 15,
        'scratch_effect' => false,
        'hover_effects' => true
    ));
} else {
    // Fallback to helper function
    wpcoupon_render_coupon_card($post, false);
}
echo '</div>';
