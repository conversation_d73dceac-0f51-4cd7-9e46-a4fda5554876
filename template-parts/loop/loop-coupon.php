<?php
/**
 * Unified AJAX loop template for coupons
 * Uses the unified coupon card system
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Use unified coupon card with proper grid wrapper
echo '<div class="coupon-grid-item">';
wpcoupon_render_coupon_card($post, 'default', array(
    'show_store_logo' => true,
    'show_badges' => true,
    'show_description' => true,
    'description_length' => 15,
    'hover_effects' => true,
    'show_image' => true
));
echo '</div>';
