<?php
/**
 * AJAX loop template for coupons
 * Uses the modern coupon card system
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Force load the modern coupon card function
$function_file = get_template_directory() . '/inc/components/modern-coupon-card.php';
if (file_exists($function_file)) {
    require_once $function_file;
}

// Set up global post data for template functions
global $post;
setup_postdata($post);

// Use modern coupon card directly (no wrapper div)
if (function_exists('render_coupon_card')) {
    try {
        render_coupon_card($post, 'default', array(
            'show_store_logo' => true,
            'show_badges' => true,
            'show_description' => true,
            'description_length' => 15,
            'show_image' => true,
            'show_voting' => true,
            'show_stats' => true
        ));
    } catch (Exception $e) {
        // If render_coupon_card fails, use detailed fallback
        echo render_ajax_coupon_fallback($post);
    }
} else {
    // If function doesn't exist, use detailed fallback
    echo render_ajax_coupon_fallback($post);
}

// Reset post data
wp_reset_postdata();

/**
 * Detailed fallback function for AJAX coupon rendering
 */
function render_ajax_coupon_fallback($coupon) {
    // Get coupon data directly
    $coupon_type = get_post_meta($coupon->ID, '_wpc_coupon_type', true) ?: 'code';
    $coupon_code = get_post_meta($coupon->ID, '_wpc_coupon_code', true) ?: '';
    $is_exclusive = get_post_meta($coupon->ID, '_wpc_exclusive', true) === 'on';
    $free_shipping = get_post_meta($coupon->ID, '_wpc_free_shipping', true) === 'on';
    $coupon_save = get_post_meta($coupon->ID, '_wpc_coupon_save', true);
    $used_count = get_post_meta($coupon->ID, '_wpc_used', true) ?: 0;
    $vote_up = get_post_meta($coupon->ID, '_wpc_vote_up', true) ?: 0;
    $vote_down = get_post_meta($coupon->ID, '_wpc_vote_down', true) ?: 0;

    // Get store info
    $store_name = '';
    $store_image = '';
    $store_terms = get_the_terms($coupon->ID, 'coupon_store');
    if ($store_terms && !is_wp_error($store_terms)) {
        $store_term = $store_terms[0];
        $store_name = $store_term->name;
        $store_image = get_term_meta($store_term->term_id, '_wpc_store_image', true);
    }

    // Get image
    $coupon_image = get_the_post_thumbnail_url($coupon->ID, 'medium');
    if (!$coupon_image && $store_image) {
        $coupon_image = $store_image;
    }

    // Success rate
    $total_votes = $vote_up + $vote_down;
    $success_rate = $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 95;

    // URLs
    $destination_url = get_post_meta($coupon->ID, '_wpc_destination_url', true) ?: get_permalink($coupon->ID);
    $coupon_href = get_permalink($coupon->ID);

    ob_start();
    ?>
    <article class="modern-coupon-card group relative bg-white rounded-2xl border border-gray-100 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 hover:border-yellow-300 default-coupon coupon-type-<?php echo esc_attr($coupon_type); ?>"
             data-coupon-id="<?php echo $coupon->ID; ?>"
             data-coupon-type="<?php echo esc_attr($coupon_type); ?>">

        <!-- Animated Background Elements -->
        <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none">
            <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-200 to-yellow-300 rounded-full group-hover:animate-pulse"></div>
            <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-tr from-blue-200 to-blue-300 rounded-full group-hover:animate-bounce"></div>
        </div>

        <!-- Coupon Image -->
        <?php if ($coupon_image) : ?>
            <div class="relative h-48 overflow-hidden">
                <img src="<?php echo esc_url($coupon_image); ?>"
                     alt="<?php echo esc_attr($coupon->post_title); ?>"
                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">

                <!-- Badges -->
                <div class="absolute top-4 right-4 flex flex-col space-y-2">
                    <?php if ($is_exclusive) : ?>
                        <div class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-3 hover:rotate-0 transition-transform">
                            ⭐ حصري
                        </div>
                    <?php endif; ?>

                    <?php if ($free_shipping) : ?>
                        <div class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform -rotate-2 hover:rotate-0 transition-transform">
                            🚚 شحن مجاني
                        </div>
                    <?php endif; ?>

                    <?php if ($coupon_save) : ?>
                        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-1 hover:rotate-0 transition-transform">
                            💰 <?php echo esc_html($coupon_save); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Type Badge -->
                <div class="absolute top-4 left-4">
                    <?php if ($coupon_type === 'code') : ?>
                        <div class="bg-blue-600 text-white px-2 py-1 rounded-lg text-xs font-bold">
                            🎫 كود
                        </div>
                    <?php else : ?>
                        <div class="bg-purple-600 text-white px-2 py-1 rounded-lg text-xs font-bold">
                            🔥 صفقة
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Card Content -->
        <div class="relative z-10 p-6">
            <!-- Store Info -->
            <?php if ($store_name) : ?>
                <div class="flex items-center space-x-3 space-x-reverse mb-4">
                    <div class="relative">
                        <div class="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0 shadow-md">
                            <?php if ($store_image) : ?>
                                <img src="<?php echo esc_url($store_image); ?>"
                                     alt="<?php echo esc_attr($store_name); ?>"
                                     class="w-full h-full object-cover">
                            <?php else : ?>
                                <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                    <?php echo esc_html(substr($store_name, 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <!-- Verified badge -->
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-bold text-gray-900 truncate"><?php echo esc_html($store_name); ?></p>
                        <div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo $success_rate; ?>% نجاح
                            </span>
                            <span>•</span>
                            <span><?php echo $used_count; ?> استخدام</span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Title -->
            <h3 class="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                <a href="<?php echo esc_url($coupon_href); ?>"
                   class="coupon-title-link coupon-button"
                   data-type="<?php echo esc_attr($coupon_type); ?>"
                   data-coupon-id="<?php echo $coupon->ID; ?>"
                   data-aff-url="<?php echo esc_attr($destination_url); ?>"
                   data-code="<?php echo esc_attr($coupon_code); ?>">
                    <?php echo esc_html($coupon->post_title); ?>
                </a>
            </h3>

            <!-- Description -->
            <p class="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                <?php
                $description = $coupon->post_excerpt ?: $coupon->post_content;
                echo esc_html(wp_trim_words($description, 15, '...'));
                ?>
            </p>

            <!-- Action Section -->
            <div class="flex items-center justify-between mb-4">
                <!-- Voting -->
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="vote-up flex items-center space-x-1 space-x-reverse text-gray-400 hover:text-green-500 transition-all duration-300 hover:scale-110"
                            data-coupon-id="<?php echo $coupon->ID; ?>">
                        <div class="w-8 h-8 rounded-full bg-gray-100 hover:bg-green-100 flex items-center justify-center transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium"><?php echo $vote_up; ?></span>
                    </button>

                    <button class="vote-down flex items-center space-x-1 space-x-reverse text-gray-400 hover:text-red-500 transition-all duration-300 hover:scale-110"
                            data-coupon-id="<?php echo $coupon->ID; ?>">
                        <div class="w-8 h-8 rounded-full bg-gray-100 hover:bg-red-100 flex items-center justify-center transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium"><?php echo $vote_down; ?></span>
                    </button>
                </div>

                <!-- Action Button -->
                <div class="coupon-button-wrapper">
                    <?php if ($coupon_type === 'code' && $coupon_code) : ?>
                        <a href="<?php echo esc_url($coupon_href); ?>"
                           class="coupon-button group/btn relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden"
                           data-coupon-id="<?php echo $coupon->ID; ?>"
                           data-type="code"
                           data-aff-url="<?php echo esc_attr($destination_url); ?>"
                           data-code="<?php echo esc_attr($coupon_code); ?>">

                            <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-500 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>

                            <svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="relative z-10">عرض الكود</span>
                        </a>
                    <?php else : ?>
                        <a href="<?php echo esc_url($coupon_href); ?>"
                           class="coupon-button group/btn relative bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden"
                           data-coupon-id="<?php echo $coupon->ID; ?>"
                           data-type="sale"
                           data-aff-url="<?php echo esc_attr($destination_url); ?>">

                            <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-500 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>

                            <svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span class="relative z-10">احصل على الصفقة</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Bottom Stats -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                <div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <span>مشاهدة مؤخراً</span>
                </div>

                <div class="text-xs text-gray-500">
                    صالح لفترة محدودة
                </div>
            </div>
        </div>
    </article>
    <?php
    return ob_get_clean();
}
