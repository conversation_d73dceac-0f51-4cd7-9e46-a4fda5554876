<?php
/**
 * AJAX loop template for coupons
 * Uses the modern coupon card system
 */

// Ensure we have a coupon object
if (!isset($post) || !$post) {
    return;
}

// Check if our modern coupon card function exists
if (!function_exists('render_coupon_card')) {
    // Fallback: Load the function file if not loaded
    $card_file = get_template_directory() . '/inc/components/modern-coupon-card.php';
    if (file_exists($card_file)) {
        require_once $card_file;
    }
}

// Use modern coupon card with error handling
echo '<div class="coupon-grid-item">';
if (function_exists('render_coupon_card')) {
    render_coupon_card($post, 'default', array(
        'show_store_logo' => true,
        'show_badges' => true,
        'show_description' => true,
        'description_length' => 15,
        'show_image' => true
    ));
} else {
    // Fallback: Simple HTML output
    echo '<div class="bg-white p-4 rounded-lg border">';
    echo '<h3 class="font-bold">' . esc_html($post->post_title) . '</h3>';
    echo '<p class="text-sm text-gray-600">' . esc_html(wp_trim_words($post->post_excerpt, 15)) . '</p>';
    echo '<a href="' . esc_url(get_permalink($post->ID)) . '" class="inline-block mt-2 bg-blue-500 text-white px-4 py-2 rounded">عرض الكوبون</a>';
    echo '</div>';
}
echo '</div>';
