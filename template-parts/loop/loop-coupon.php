<?php
// Setup coupon and store objects using proper template functions
$coupon_obj = wpcoupon_coupon();
$store_obj = wpcoupon_store();

$has_thumb = wpcoupon_maybe_show_coupon_thumb();
$has_expired = $coupon_obj->has_expired();
$coupon_type = $coupon_obj->get_type();
$is_featured = get_post_meta($coupon_obj->ID, '_wpc_exclusive', true);
$coupon_code = $coupon_obj->get_code();
$coupon_expires = $coupon_obj->get_expires();
?>
<!-- Tailwind CSS Coupon Card -->
<div data-id="<?php echo $coupon_obj->ID; ?>"
     class="coupon-card group relative overflow-hidden transition-all duration-300 hover:shadow-coupon-hover <?php echo $has_thumb ? 'has-thumb' : 'no-thumb'; ?> c-cat c-type-<?php echo esc_attr( $coupon_type ); ?> <?php echo ( $has_expired ) ? 'coupon-expired opacity-75' : 'coupon-live'; ?> <?php echo $is_featured ? 'ring-2 ring-orange-400' : ''; ?>">

    <?php if ( $has_expired ) { ?>
        <!-- Expired Badge -->
        <div class="absolute top-2 right-2 z-10">
            <span class="coupon-badge-expired text-xs px-2 py-1">
                <?php esc_html_e( 'منتهي الصلاحية', 'wp-coupon' ); ?>
            </span>
        </div>
    <?php } ?>

    <?php if ( $is_featured ) { ?>
        <!-- Featured Badge -->
        <div class="absolute top-2 left-2 z-10">
            <span class="coupon-badge-featured text-xs px-2 py-1">
                <?php esc_html_e( 'حصري', 'wp-coupon' ); ?>
            </span>
        </div>
    <?php } ?>

    <!-- Store Thumbnail Section -->
    <?php if ( $has_thumb ) { ?>
    <div class="coupon-card-header bg-gradient-to-r from-gray-50 to-gray-100 p-4">
        <div class="flex items-center space-x-3 space-x-reverse">
            <div class="store-thumb-container">
                <?php wpcoupon_thumb( $has_thumb === 'save_value' ? true : false ); ?>
            </div>
            <div class="store-info flex-1">
                <a href="<?php echo esc_url($coupon_obj->get_store_url()); ?>"
                   class="store-name text-sm font-medium text-secondary-700 hover:text-secondary-800 transition-colors duration-200">
                    <?php printf( esc_html__( '%s Coupons', 'wp-coupon' ), esc_html($store_obj->name) ); ?>
                    <svg class="inline w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
    <?php } ?>

    <!-- Coupon Content Section -->
    <div class="coupon-card-body">
        <!-- Coupon Type Badge -->
        <div class="mb-3">
            <?php
            switch ( $coupon_type ) {
                case 'code':
                    echo '<span class="coupon-badge-code">' . esc_html__( 'كود خصم', 'wp-coupon' ) . '</span>';
                    break;
                case 'sale':
                    echo '<span class="coupon-badge-sale">' . esc_html__( 'عرض', 'wp-coupon' ) . '</span>';
                    break;
                case 'print':
                    echo '<span class="coupon-badge-print">' . esc_html__( 'قابل للطباعة', 'wp-coupon' ) . '</span>';
                    break;
            }
            ?>
        </div>

        <!-- Coupon Title -->
        <h3 class="coupon-title text-lg font-bold text-gray-900 mb-3 leading-tight">
            <?php edit_post_link('<svg class="inline w-4 h-4 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z"></path></svg>', '', '', $coupon_obj->ID ); ?>
            <a class="coupon-link text-gray-900 hover:text-secondary-600 transition-colors duration-200"
               <?php if ( ! wpcoupon_is_single_enable() ) { ?>
               <?php } ?>
               title="<?php echo esc_attr( get_the_title( $coupon_obj->ID ) ) ?>"
               data-type="<?php echo esc_attr($coupon_type); ?>"
               data-coupon-id="<?php echo $coupon_obj->ID; ?>"
               data-aff-url="<?php echo esc_attr( $coupon_obj->get_destination_url() ); ?>"
               data-code="<?php echo esc_attr( $coupon_code ); ?>"
               href="<?php echo esc_url( $coupon_obj->get_href() ); ?>">
               <?php echo get_the_title( $coupon_obj->ID ); ?>
            </a>
        </h3>

        <!-- Coupon Description -->
        <div class="coupon-description text-gray-600 text-sm mb-4 leading-relaxed">
            <?php if (  wpcoupon_get_option( 'coupon_more_desc', true ) ) { ?>
                <div class="coupon-des-ellip"><?php
                    echo  $coupon_obj->get_excerpt(
                        false,
                        '<span class="text-secondary-600 hover:text-secondary-700 cursor-pointer font-medium">...<a class="more" href="#">'.esc_html__( 'المزيد', 'wp-coupon' ).'</a></span>',
                        $has_thumb
                    );
                    ?></div>
                <?php
                if ( $coupon_obj->has_more_content ) {
                    ?>
                    <div class="coupon-des-full hidden"><?php
                        echo str_replace( ']]>', ']]>',  apply_filters( 'the_content', $coupon_obj->post_content.' <a class="more less text-secondary-600 hover:text-secondary-700 cursor-pointer font-medium" href="#">'. esc_html__( 'أقل', 'wp-coupon') .'</a>' ) );
                        ?></div>
                <?php } ?>
            <?php } else { ?>
                <?php echo apply_filters( 'the_content', $coupon_obj->post_content ); ?>
            <?php } ?>
        </div>
    </div>

    <!-- Coupon Action Section -->
    <div class="coupon-card-footer bg-gray-50">
        <div class="flex items-center justify-between mb-3">
            <!-- Main Action Button -->
            <div class="flex-1 mr-3">
                <?php
                switch ( $coupon_type ) {
                    case 'sale':
                        ?>
                        <a data-type="<?php echo esc_attr($coupon_type); ?>"
                           data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                           data-aff-url="<?php echo esc_attr( $coupon_obj->get_destination_url() ); ?>"
                           class="btn-secondary w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary-300"
                           href="<?php echo esc_url( $coupon_obj->get_href() ); ?>">
                            <?php esc_html_e( 'احصل علي العرض', 'wp-coupon' ); ?>
                            <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </a>
                        <?php
                        break;
                    case 'print':
                        ?>
                        <a data-type="<?php echo esc_attr($coupon_type); ?>"
                           data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                           data-aff-url="<?php echo esc_attr( $coupon_obj->get_destination_url() ); ?>"
                           class="bg-green-500 hover:bg-green-600 text-white w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-300"
                           href="<?php echo esc_url( $coupon_obj->get_href() ); ?>">
                            <?php esc_html_e( 'اطبع الكوبون', 'wp-coupon' ); ?>
                            <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                        <?php
                        break;
                    default:
                        ?>
                        <a data-type="<?php echo esc_attr($coupon_type); ?>"
                           data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                           href="<?php echo esc_url( $coupon_obj->get_href() ); ?>"
                           class="btn-primary w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-400 group"
                           data-tooltip="<?php echo esc_attr_e( 'اضغط لنسخ الكود وفتح الموقع', 'wp-coupon' ); ?>"
                           data-code="<?php echo esc_attr( $coupon_code ); ?>"
                           data-aff-url="<?php echo esc_attr( $coupon_obj->get_destination_url() ); ?>">
                            <div class="flex items-center justify-center">
                                <span class="code-text font-mono text-lg font-bold mr-2 bg-white bg-opacity-20 px-2 py-1 rounded">
                                    <?php echo esc_html( $coupon_obj->get_code( 8 , true ) ); ?>
                                </span>
                                <span class="get-code"><?php  esc_html_e( 'عرض الكوبون', 'wp-coupon' ); ?></span>
                            </div>
                        </a>
                        <?php
                }
                ?>
            </div>

            <!-- Save Button -->
            <div class="flex-shrink-0">
                <button data-tooltip="<?php esc_attr_e( "Save this coupon", 'wp-coupon' ); ?>"
                        data-coupon-id="<?php echo wpcoupon_coupon()->ID; ?>"
                        class="add-coupon-favorite coupon-save p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-300">
                    <svg class="w-5 h-5 text-gray-400 hover:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Expiry Information -->
        <div class="text-xs text-gray-500 text-center">
            <?php if ( ! $has_expired ) { ?>
                <span class="flex items-center justify-center">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <?php printf( esc_html__( 'Expires %s', 'wp-coupon' ), esc_html($coupon_expires) ); ?>
                </span>
            <?php } else { ?>
                <span class="text-red-500 font-medium">
                    <?php echo esc_html($coupon_expires); ?>
                </span>
            <?php } ?>
        </div>
    </div>

    <?php
    //get_template_part('template-parts/components/coupon-modal');
    ?>
</div>
