<?php
$has_thumb = wpcoupon_maybe_show_coupon_thumb();
$has_expired = wpcoupon_coupon()->has_expired();
$coupon_type = wpcoupon_coupon()->get_type();
$is_featured = get_post_meta( wpcoupon_coupon()->ID, '_wpc_exclusive', true );
?>
<!-- Tailwind CSS Coupon Card - Category View -->
<div data-id="<?php echo wpcoupon_coupon()->ID; ?>"
	class="coupon-card group relative overflow-hidden transition-all duration-300 hover:shadow-coupon-hover <?php echo $has_thumb ? 'has-thumb' : 'no-thumb'; ?> c-cat c-type-<?php echo esc_attr( $coupon_type ); ?> <?php echo ( $has_expired ) ? 'coupon-expired opacity-75' : 'coupon-live'; ?> <?php echo $is_featured ? 'ring-2 ring-orange-400' : ''; ?>">

	<?php if ( $has_expired ) { ?>
        <!-- Expired Badge -->
        <div class="absolute top-2 right-2 z-10">
            <span class="coupon-badge-expired text-xs px-2 py-1">
                <?php esc_html_e( 'منتهي الصلاحية', 'wp-coupon' ); ?>
            </span>
        </div>
    <?php } ?>

    <?php if ( $is_featured ) { ?>
        <!-- Featured Badge -->
        <div class="absolute top-2 left-2 z-10">
            <span class="coupon-badge-featured text-xs px-2 py-1">
                <?php esc_html_e( 'حصري', 'wp-coupon' ); ?>
            </span>
        </div>
    <?php } ?>

	<!-- Store Thumbnail Section -->
	<?php if ( $has_thumb ) { ?>
	<div class="coupon-card-header bg-gradient-to-r from-gray-50 to-gray-100 p-3">
		<div class="flex justify-center">
			<?php wpcoupon_thumb( $has_thumb === 'save_value' ? true : false ); ?>
		</div>
	</div>
	<?php } ?>

	<!-- Coupon Content Section -->
	<div class="coupon-card-body">
		<!-- Coupon Type Badge -->
        <div class="mb-3">
            <?php
            switch ( $coupon_type ) {
                case 'code':
                    echo '<span class="coupon-badge-code">' . esc_html__( 'كود خصم', 'wp-coupon' ) . '</span>';
                    break;
                case 'sale':
                    echo '<span class="coupon-badge-sale">' . esc_html__( 'عرض', 'wp-coupon' ) . '</span>';
                    break;
                case 'print':
                    echo '<span class="coupon-badge-print">' . esc_html__( 'قابل للطباعة', 'wp-coupon' ) . '</span>';
                    break;
            }
            ?>
        </div>

		<!-- Coupon Title -->
		<h3 class="coupon-title text-lg font-bold text-gray-900 mb-3 leading-tight">
			<a class="coupon-link text-gray-900 hover:text-secondary-600 transition-colors duration-200" <?php if ( ! wpcoupon_is_single_enable() ) { ?> <?php } ?>
				title="<?php echo esc_attr( get_the_title( wpcoupon_coupon()->ID ) ) ?>"
				data-type="<?php echo wpcoupon_coupon()->get_type(); ?>"
				data-coupon-id="<?php echo wpcoupon_coupon()->ID; ?>"
				data-aff-url="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>"
				data-code="<?php echo esc_attr( wpcoupon_coupon()->get_code() ); ?>"
				href="<?php echo esc_attr( wpcoupon_coupon()->get_href() ); ?>">
				<?php echo get_the_title( wpcoupon_coupon()->ID ); ?>
			</a>
		</h3>

		<!-- Coupon Description -->
		<div class="coupon-description text-gray-600 text-sm mb-4 leading-relaxed">
			<?php if (  wpcoupon_get_option( 'coupon_more_desc', true ) ) { ?>
			<div class="coupon-des-ellip"><?php
                    echo  wpcoupon_coupon()->get_excerpt(
                        false,
                        '<span class="text-secondary-600 hover:text-secondary-700 cursor-pointer font-medium">...<a class="more" href="#">'.esc_html__( 'المزيد', 'wp-coupon' ).'</a></span>',
                        $has_thumb
                    );
                    ?></div>
			<?php
                if ( wpcoupon_coupon()->has_more_content ) {
                    ?>
			<div class="coupon-des-full hidden"><?php
                        echo str_replace( ']]>', ']]&gt;',  apply_filters( 'the_content', wpcoupon_coupon()->post_content.' <a class="more less text-secondary-600 hover:text-secondary-700 cursor-pointer font-medium" href="#">'. esc_html__( 'أقل', 'wp-coupon') .'</a>' ) );
                        ?></div>
			<?php } ?>
			<?php } else { ?>
			<?php echo apply_filters( 'the_content', wpcoupon_coupon()->post_content ); ?>
			<?php } ?>
		</div>
	</div>

	<!-- Coupon Action Section -->
	<div class="coupon-card-footer bg-gray-50">
		<!-- Main Action Button -->
		<div class="mb-3">
			<?php
			switch ( wpcoupon_coupon()->get_type() ) {
				case 'sale':
					?>
					<a data-type="<?php echo wpcoupon_coupon()->get_type(); ?>"
					   data-coupon-id="<?php echo wpcoupon_coupon()->ID; ?>"
					   data-aff-url="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>"
					   class="btn-secondary w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary-300"
					   href="<?php echo esc_attr( wpcoupon_coupon()->get_href() ); ?>">
						<?php esc_html_e( 'احصل علي العرض', 'wp-coupon' ); ?>
						<svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
						</svg>
					</a>
					<?php
					break;
				case 'print':
					?>
					<a data-type="<?php echo wpcoupon_coupon()->get_type(); ?>"
					   data-coupon-id="<?php echo wpcoupon_coupon()->ID; ?>"
					   data-aff-url="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>"
					   class="bg-green-500 hover:bg-green-600 text-white w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-300"
					   href="<?php echo esc_attr( wpcoupon_coupon()->get_href() ); ?>">
						<?php esc_html_e( 'اطبع الكوبون', 'wp-coupon' ); ?>
						<svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path>
						</svg>
					</a>
					<?php
					break;
				default:
					?>
					<a data-type="<?php echo wpcoupon_coupon()->get_type(); ?>"
					   data-coupon-id="<?php echo wpcoupon_coupon()->ID; ?>"
					   href="<?php echo esc_attr( wpcoupon_coupon()->get_href() ); ?>"
					   class="btn-primary w-full text-center py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-400 group"
					   data-tooltip="<?php echo esc_attr_e( 'اضغط لنسخ الكود وفتح الموقع', 'wp-coupon' ); ?>"
					   data-code="<?php echo esc_attr( wpcoupon_coupon()->get_code() ); ?>"
					   data-aff-url="<?php echo esc_attr( wpcoupon_coupon()->get_go_out_url() ); ?>">
						<div class="flex items-center justify-center">
							<span class="code-text font-mono text-lg font-bold mr-2 bg-white bg-opacity-20 px-2 py-1 rounded">
								<?php echo esc_html( wpcoupon_coupon()->get_code( 8 , true ) ); ?>
							</span>
							<span class="get-code"><?php  esc_html_e( 'عرض الكوبون', 'wp-coupon' ); ?></span>
						</div>
					</a>
					<?php
			}
			?>
		</div>

		<!-- Coupon Details -->
		<div class="grid grid-cols-3 gap-2 text-xs text-gray-600 mb-3">
			<!-- Discount Value -->
			<?php $discount_value = get_post_meta( wpcoupon_coupon()->ID, '_wpc_coupon_save', true ); ?>
			<?php if ( $discount_value ) { ?>
			<div class="flex items-center justify-center bg-white rounded-lg p-2">
				<svg class="w-3 h-3 mr-1 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
				</svg>
				<span class="font-medium"><?php echo esc_html( $discount_value ); ?></span>
			</div>
			<?php } ?>

			<!-- Total Used -->
			<div class="flex items-center justify-center bg-white rounded-lg p-2">
				<svg class="w-3 h-3 mr-1 text-secondary-500" fill="currentColor" viewBox="0 0 20 20">
					<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<span><?php echo wpcoupon_coupon()->get_total_used(); ?> <?php esc_html_e( 'مستخدم', 'wpcoupon' ) ?></span>
			</div>

			<!-- Coupon Type -->
			<div class="flex items-center justify-center bg-white rounded-lg p-2">
				<span class="font-medium text-gray-700"><?php echo wpcoupon_coupon()->get_coupon_type_text(); ?></span>
			</div>
		</div>
	</div>

	<?php
    //get_template_part('template-parts/components/coupon-modal');
    ?>
</div>