<?php
/**
 * Modern Coupon Card Template
 * Creative modern design with branded yellow colors
 * Dynamic buttons based on coupon type
 * Uses title and button links logic from old cards
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Modern Coupon Card -->
<article class="<?php echo esc_attr(implode(' ', $card_classes)); ?>" 
         data-coupon-id="<?php echo $coupon->ID; ?>"
         data-coupon-type="<?php echo esc_attr($coupon_type); ?>">

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none">
        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-200 to-yellow-300 rounded-full group-hover:animate-pulse"></div>
        <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-tr from-blue-200 to-blue-300 rounded-full group-hover:animate-bounce"></div>
    </div>

    <!-- Enhanced Coupon Image with Dynamic Placeholder -->
    <?php if ($options['show_image']) : ?>
        <div class="relative h-48 overflow-hidden">
            <?php
            // Enhanced image classes based on image type
            $image_classes = 'w-full h-full transition-transform duration-700 group-hover:scale-110';
            if (isset($image_type) && $image_type === 'placeholder') {
                $image_classes .= ' object-contain bg-gradient-to-br from-gray-50 to-gray-100';
            } else {
                $image_classes .= ' object-cover';
            }
            ?>
            <img src="<?php echo (strpos($coupon_image, 'data:') === 0) ? $coupon_image : esc_url($coupon_image); ?>"
                 alt="<?php echo esc_attr($coupon->post_title); ?>"
                 class="<?php echo $image_classes; ?>">

            <!-- Creative Badges Overlay -->
            <?php if ($options['show_badges']) : ?>
                <div class="absolute top-4 right-4 flex flex-col space-y-2">
                    <?php if ($is_exclusive) : ?>
                        <div class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-3 hover:rotate-0 transition-transform">
                            ⭐ حصري
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($free_shipping) : ?>
                        <div class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform -rotate-2 hover:rotate-0 transition-transform">
                            🚚 شحن مجاني
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($coupon_save) : ?>
                        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg transform rotate-1 hover:rotate-0 transition-transform">
                            💰 <?php echo esc_html($coupon_save); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Coupon Type Badge -->
            <div class="absolute top-4 left-4">
                <?php if ($coupon_type === 'code') : ?>
                    <div class="bg-blue-600 text-white px-2 py-1 rounded-lg text-xs font-bold">
                        🎫 كود
                    </div>
                <?php else : ?>
                    <div class="bg-purple-600 text-white px-2 py-1 rounded-lg text-xs font-bold">
                        🔥 صفقة
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Card Content -->
    <div class="relative z-10 p-6">

        <!-- Store Info with Modern Design -->
        <?php if ($options['show_store_logo'] && $store_name) : ?>
            <div class="flex items-center space-x-3 space-x-reverse mb-4">
                <div class="relative">
                    <div class="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0 shadow-md">
                        <?php if ($store_image) : ?>
                            <img src="<?php echo esc_url($store_image); ?>" 
                                 alt="<?php echo esc_attr($store_name); ?>"
                                 class="w-full h-full object-cover">
                        <?php else : ?>
                            <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                <?php echo esc_html(substr($store_name, 0, 2)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <!-- Store verified badge -->
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-bold text-gray-900 truncate"><?php echo esc_html($store_name); ?></p>
                    <?php if ($options['show_stats']) : ?>
                        <div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo $success_rate; ?>% نجاح
                            </span>
                            <span>•</span>
                            <span><?php echo $used_count; ?> استخدام</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Coupon Title with Same Logic as Old Cards -->
        <h3 class="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
            <a href="<?php echo esc_url($coupon_href); ?>" 
               class="coupon-title-link coupon-button"
               data-type="<?php echo esc_attr($coupon_type); ?>"
               data-coupon-id="<?php echo $coupon->ID; ?>"
               data-aff-url="<?php echo esc_attr($destination_url); ?>"
               data-code="<?php echo esc_attr($coupon_code); ?>">
                <?php echo esc_html($coupon->post_title); ?>
            </a>
        </h3>

        <!-- Description -->
        <?php if ($options['show_description']) : ?>
            <p class="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                <?php 
                $description = $coupon->post_excerpt ?: $coupon->post_content;
                echo esc_html(wp_trim_words($description, $options['description_length'], '...'));
                ?>
            </p>
        <?php endif; ?>

        <!-- Action Section -->
        <div class="flex items-center justify-between mb-4">
            
            <!-- Voting with Modern Design -->
            <?php if ($options['show_voting']) : ?>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="vote-up flex items-center space-x-1 space-x-reverse text-gray-400 hover:text-green-500 transition-all duration-300 hover:scale-110"
                            data-coupon-id="<?php echo $coupon->ID; ?>">
                        <div class="w-8 h-8 rounded-full bg-gray-100 hover:bg-green-100 flex items-center justify-center transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium"><?php echo $vote_up; ?></span>
                    </button>
                    
                    <button class="vote-down flex items-center space-x-1 space-x-reverse text-gray-400 hover:text-red-500 transition-all duration-300 hover:scale-110"
                            data-coupon-id="<?php echo $coupon->ID; ?>">
                        <div class="w-8 h-8 rounded-full bg-gray-100 hover:bg-red-100 flex items-center justify-center transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium"><?php echo $vote_down; ?></span>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Dynamic Action Button Based on Coupon Type -->
            <div class="coupon-button-wrapper">
                <?php if ($coupon_type === 'code' && $coupon_code) : ?>
                    <!-- Code Button with Creative Design -->
                    <a href="<?php echo esc_url($coupon_href); ?>"
                       class="coupon-button group/btn relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden"
                       data-coupon-id="<?php echo $coupon->ID; ?>"
                       data-type="code"
                       data-aff-url="<?php echo esc_attr($destination_url); ?>"
                       data-code="<?php echo esc_attr($coupon_code); ?>">
                        
                        <!-- Button background animation -->
                        <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-500 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                        
                        <svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="relative z-10">عرض الكود</span>
                    </a>
                    
                <?php else : ?>
                    <!-- Deal Button with Creative Design -->
                    <a href="<?php echo esc_url($coupon_href); ?>"
                       class="coupon-button group/btn relative bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl font-bold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center overflow-hidden"
                       data-coupon-id="<?php echo $coupon->ID; ?>"
                       data-type="sale"
                       data-aff-url="<?php echo esc_attr($destination_url); ?>">
                        
                        <!-- Button background animation -->
                        <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-500 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                        
                        <svg class="w-5 h-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        <span class="relative z-10">احصل على الصفقة</span>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Bottom Stats Bar -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
            <div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span>مشاهدة مؤخراً</span>
            </div>
            
            <div class="text-xs text-gray-500">
                صالح لفترة محدودة
            </div>
        </div>
    </div>
</article>
