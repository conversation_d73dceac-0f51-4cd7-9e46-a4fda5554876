<?php
/**
 * Simple Store Card Component
 * Creative modern store card design with proper links
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get store data
$store_obj = wpcoupon_store();
if (!$store_obj) {
    return;
}

// Get template variables
$style = get_query_var('store_card_style', 'featured');
$args = get_query_var('store_card_args', array());

// Store information
$store_name = $store_obj->name;
$store_url = $store_obj->get_url();
$store_page_link = get_term_link($store_obj->term_id, 'coupon_store'); // Single store page link
$store_external_link = $store_obj->get_go_store_url(); // External store link
$coupon_count = $store_obj->count;

// Get store image with fallbacks
$store_image = '';
$image_id = get_term_meta($store_obj->term_id, '_wpc_store_image_id', true);
if ($image_id && intval($image_id) > 0) {
    $store_image = wp_get_attachment_image_url($image_id, 'medium');
}

if (!$store_image) {
    $store_image_meta = get_term_meta($store_obj->term_id, '_wpc_store_image', true);
    if ($store_image_meta) {
        $store_image = $store_image_meta;
    }
}

if (!$store_image && $store_url) {
    $store_image = 'https://s.wordpress.com/mshots/v1/'.urlencode($store_url).'?w=200&h=115';
}

if (!$store_image) {
    $store_image = get_template_directory_uri() . '/assets/images/store-placeholder.png';
}

// Creative modern card classes based on style
$card_classes = 'simple-store-card group relative overflow-hidden transition-all duration-500 cursor-pointer';

switch ($style) {
    case 'slider':
        $card_classes .= ' bg-gradient-to-br from-white via-gray-50 to-primary-50 rounded-3xl shadow-xl hover:shadow-2xl transform hover:-translate-y-3 hover:rotate-1 border border-primary-100 backdrop-blur-sm';
        break;
    case 'featured':
        $card_classes .= ' bg-gradient-to-br from-primary-50 via-white to-secondary-50 rounded-3xl shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 border-2 border-gradient-to-r from-primary-200 to-secondary-200';
        break;
    case 'grid':
        $card_classes .= ' bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-2 hover:rotate-0.5 border border-gray-200 hover:border-primary-300';
        break;
    case 'minimal':
        $card_classes .= ' bg-white rounded-xl shadow-md hover:shadow-lg transform hover:-translate-y-1 border border-gray-100 hover:border-primary-200';
        break;
    default:
        $card_classes .= ' bg-white rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-2';
        break;
}
?>

<div class="<?php echo esc_attr($card_classes); ?>">
    <!-- Store Image with Creative Design -->
    <div class="store-image relative overflow-hidden <?php echo $style === 'slider' ? 'h-36' : 'h-44'; ?> rounded-t-3xl">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-100/30 via-transparent to-secondary-100/30"></div>

        <!-- Store Image -->
        <img src="<?php echo esc_url($store_image); ?>"
             alt="<?php echo esc_attr($store_name); ?>"
             class="w-full h-full object-cover transition-all duration-700 group-hover:scale-125 group-hover:rotate-2"
             loading="lazy">

        <!-- Creative Overlay with Glassmorphism -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

        <!-- Floating Badge with Animation -->
        <?php if ($args['show_badge'] && !empty($args['badge_text'])) : ?>
            <div class="absolute top-4 <?php echo is_rtl() ? 'left-4' : 'right-4'; ?> transform group-hover:scale-110 transition-transform duration-300">
                <span class="inline-flex items-center px-4 py-2 rounded-2xl text-xs font-bold bg-gradient-to-r from-primary-500 via-primary-600 to-secondary-500 text-white shadow-xl backdrop-blur-sm border border-white/20 animate-pulse">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <?php echo esc_html($args['badge_text']); ?>
                </span>
            </div>
        <?php endif; ?>

        <!-- Interactive Overlay for Slider Style -->
        <?php if ($style === 'slider') : ?>
            <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform group-hover:scale-105">
                <div class="bg-white/95 backdrop-blur-md rounded-2xl px-6 py-3 shadow-2xl border border-white/50 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                    <a href="<?php echo esc_url($store_external_link); ?>"
                       class="inline-flex items-center gap-3 text-gray-900 font-bold text-sm hover:text-primary-600 transition-colors duration-200"
                       target="_blank"
                       rel="noopener">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        <?php esc_html_e('زيارة المتجر', 'wp-coupon'); ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Decorative Elements -->
        <div class="absolute top-2 left-2 w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
        <div class="absolute bottom-2 right-2 w-6 h-6 bg-gradient-to-br from-secondary-400 to-primary-400 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
    </div>

    <!-- Store Content with Creative Design -->
    <div class="store-content p-6 <?php echo $style === 'minimal' ? 'p-4' : 'p-6'; ?> relative">
        <!-- Background Decoration -->
        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 via-secondary-400 to-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>

        <!-- Store Name with Creative Typography -->
        <h3 class="store-name font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-primary-600 group-hover:to-secondary-600 transition-all duration-300 <?php echo $style === 'slider' ? 'text-base' : 'text-lg'; ?>">
            <a href="<?php echo esc_url($store_page_link); ?>"
               class="hover:no-underline block relative">
                <?php echo esc_html($store_name); ?>
                <!-- Animated underline -->
                <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 group-hover:w-full transition-all duration-500"></span>
            </a>
        </h3>

        <!-- Coupons Count with Creative Design -->
        <?php if ($args['show_coupons_count']) : ?>
            <div class="coupons-count flex items-center gap-3 text-gray-600 mb-4 <?php echo $style === 'slider' ? 'text-sm' : 'text-base'; ?>">
                <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-xl group-hover:from-primary-200 group-hover:to-secondary-200 transition-colors duration-300">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <span class="font-medium">
                    <?php
                    printf(
                        _n('%d كوبون متاح', '%d كوبون متاح', $coupon_count, 'wp-coupon'),
                        $coupon_count
                    );
                    ?>
                </span>
            </div>
        <?php endif; ?>

        <!-- Action Buttons (for non-slider styles) -->
        <?php if ($style !== 'slider') : ?>
            <div class="store-actions flex gap-2">
                <!-- View Store Page Button -->
                <a href="<?php echo esc_url($store_page_link); ?>"
                   class="flex-1 inline-flex items-center justify-center gap-2 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl <?php echo $style === 'minimal' ? 'text-xs py-2' : 'text-sm'; ?> group/btn">
                    <svg class="w-4 h-4 transition-transform duration-300 group-hover/btn:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <?php esc_html_e('عرض الكوبونات', 'wp-coupon'); ?>
                </a>

                <!-- Visit Store Button -->
                <a href="<?php echo esc_url($store_external_link); ?>"
                   target="_blank"
                   rel="noopener"
                   class="inline-flex items-center justify-center gap-2 bg-white border-2 border-gray-200 hover:border-primary-300 text-gray-700 hover:text-primary-600 font-semibold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg <?php echo $style === 'minimal' ? 'text-xs py-2' : 'text-sm'; ?> group/visit">
                    <svg class="w-4 h-4 transition-transform duration-300 group-hover/visit:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Creative Hover Effects -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-transparent to-secondary-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none rounded-3xl"></div>

    <!-- Floating Animation Elements -->
    <div class="absolute -top-2 -right-2 w-4 h-4 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-700 transform group-hover:translate-y-2 group-hover:translate-x-2"></div>
    <div class="absolute -bottom-2 -left-2 w-3 h-3 bg-gradient-to-br from-secondary-400 to-primary-400 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-700 delay-100 transform group-hover:-translate-y-2 group-hover:-translate-x-2"></div>
</div>
