<?php
/**
 * Simple Store Card Component
 * Clean and flexible store card design
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get store data
$store_obj = wpcoupon_store();
if (!$store_obj) {
    return;
}

// Get template variables
$style = get_query_var('store_card_style', 'featured');
$args = get_query_var('store_card_args', array());

// Store information
$store_name = $store_obj->name;
$store_url = $store_obj->get_url();
$store_link = $store_obj->get_go_store_url();
$coupon_count = $store_obj->count;

// Get store image with fallbacks
$store_image = '';
$image_id = get_term_meta($store_obj->term_id, '_wpc_store_image_id', true);
if ($image_id && intval($image_id) > 0) {
    $store_image = wp_get_attachment_image_url($image_id, 'medium');
}

if (!$store_image) {
    $store_image_meta = get_term_meta($store_obj->term_id, '_wpc_store_image', true);
    if ($store_image_meta) {
        $store_image = $store_image_meta;
    }
}

if (!$store_image && $store_url) {
    $store_image = 'https://s.wordpress.com/mshots/v1/'.urlencode($store_url).'?w=200&h=115';
}

if (!$store_image) {
    $store_image = get_template_directory_uri() . '/assets/images/store-placeholder.png';
}

// Card classes based on style
$card_classes = 'store-card group relative overflow-hidden transition-all duration-300';

switch ($style) {
    case 'slider':
        $card_classes .= ' bg-white rounded-2xl shadow-lg hover:shadow-2xl transform hover:-translate-y-2 border border-gray-100';
        break;
    case 'featured':
        $card_classes .= ' bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 border-2 border-primary-100';
        break;
    case 'grid':
        $card_classes .= ' bg-white rounded-xl shadow-md hover:shadow-lg transform hover:-translate-y-1 border border-gray-200';
        break;
    case 'minimal':
        $card_classes .= ' bg-white rounded-lg shadow-sm hover:shadow-md border border-gray-100';
        break;
    default:
        $card_classes .= ' bg-white rounded-xl shadow-md hover:shadow-lg';
        break;
}
?>

<div class="<?php echo esc_attr($card_classes); ?>">
    <!-- Store Image -->
    <div class="store-image relative overflow-hidden <?php echo $style === 'slider' ? 'h-32' : 'h-40'; ?>">
        <img src="<?php echo esc_url($store_image); ?>" 
             alt="<?php echo esc_attr($store_name); ?>"
             class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
             loading="lazy">
        
        <!-- Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <!-- Badge -->
        <?php if ($args['show_badge'] && !empty($args['badge_text'])) : ?>
            <div class="absolute top-3 <?php echo is_rtl() ? 'left-3' : 'right-3'; ?>">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg">
                    <?php echo esc_html($args['badge_text']); ?>
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Quick View Button (for slider style) -->
        <?php if ($style === 'slider') : ?>
            <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <a href="<?php echo esc_url($store_link); ?>" 
                   class="inline-flex items-center gap-2 bg-white/90 backdrop-blur-sm text-gray-900 px-4 py-2 rounded-full font-semibold text-sm hover:bg-white transition-colors duration-200"
                   target="<?php echo esc_attr($args['link_target']); ?>">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <?php esc_html_e('عرض المتجر', 'wp-coupon'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Store Content -->
    <div class="store-content p-4 <?php echo $style === 'minimal' ? 'p-3' : 'p-4'; ?>">
        <!-- Store Name -->
        <h3 class="store-name font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200 <?php echo $style === 'slider' ? 'text-sm' : 'text-base'; ?>">
            <a href="<?php echo esc_url($store_link); ?>" 
               target="<?php echo esc_attr($args['link_target']); ?>"
               class="hover:underline">
                <?php echo esc_html($store_name); ?>
            </a>
        </h3>
        
        <!-- Coupons Count -->
        <?php if ($args['show_coupons_count']) : ?>
            <div class="coupons-count flex items-center gap-2 text-gray-600 mb-3 <?php echo $style === 'slider' ? 'text-xs' : 'text-sm'; ?>">
                <svg class="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span>
                    <?php 
                    printf(
                        _n('%d كوبون متاح', '%d كوبون متاح', $coupon_count, 'wp-coupon'),
                        $coupon_count
                    ); 
                    ?>
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Action Button (for non-slider styles) -->
        <?php if ($style !== 'slider') : ?>
            <div class="store-actions">
                <a href="<?php echo esc_url($store_link); ?>" 
                   target="<?php echo esc_attr($args['link_target']); ?>"
                   class="inline-flex items-center justify-center w-full gap-2 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-semibold py-2.5 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 <?php echo $style === 'minimal' ? 'text-xs py-2' : 'text-sm'; ?>">
                    <?php esc_html_e('عرض الكوبونات', 'wp-coupon'); ?>
                    <svg class="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1 rtl:group-hover:-translate-x-1" 
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Hover Effect Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-inherit"></div>
</div>
