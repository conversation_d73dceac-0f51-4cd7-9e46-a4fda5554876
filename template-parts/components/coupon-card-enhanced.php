<?php
/**
 * Minimal & Creative Coupon Card Component
 * Modern, clean design with excellent UX
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get coupon data - should be set up before including this template
$coupon_obj = wpcoupon_coupon();

if (!$coupon_obj) {
    return; // No coupon data available
}

// Get store information directly from the coupon
$store_obj = $coupon_obj->store;

// Set store defaults if no store available
if (!$store_obj) {
    $store_name = esc_html__('متجر غير محدد', 'wp-coupon');
    $store_url = '#';
} else {
    $store_name = $store_obj->get_display_name();
    $store_url = $store_obj->get_url();
}

// Get coupon thumbnail using the existing coupon class method
$coupon_thumbnail = '';
$has_coupon_thumb = false;

// Use the existing coupon get_thumb method which handles all the logic
$coupon_thumbnail = $coupon_obj->get_thumb('medium', false, true); // Get URL only

if ($coupon_thumbnail) {
    $has_coupon_thumb = true;
}

// Debug information (remove in production)
if (WP_DEBUG) {
    error_log('Coupon Card Debug - Coupon ID: ' . $coupon_obj->ID);
    error_log('Coupon Thumbnail URL: ' . ($coupon_thumbnail ? $coupon_thumbnail : 'No thumbnail'));
    error_log('Has Coupon Thumb: ' . ($has_coupon_thumb ? 'Yes' : 'No'));
}

// Get component configuration - simplified to just featured or default
$is_featured_card = get_query_var('coupon_card_is_featured', false);

// Get coupon information
$has_expired = $coupon_obj->has_expired();
$coupon_type = $coupon_obj->get_type();
$is_featured = $coupon_obj->is_exclusive();
$coupon_code = $coupon_obj->get_code();
$coupon_expires = $coupon_obj->get_expires();
$success_rate = $coupon_obj->percent_success();

// Build CSS classes - simplified design with smaller fonts
$card_classes = [
    'bg-white',
    'rounded-xl',
    'border',
    'border-gray-100',
    'hover:border-primary-200',
    'hover:shadow-md',
    'transition-all',
    'duration-300',
    'overflow-hidden',
    'cursor-pointer',
    'group'
];

if ($has_expired) {
    $card_classes[] = 'opacity-60 grayscale pointer-events-none';
}

if ($is_featured || $is_featured_card) {
    $card_classes[] = 'border-primary-200 bg-gradient-to-br from-primary-50 to-white ring-1 ring-primary-100';
}

// Determine coupon action text and style
$coupon_actions = array(
    'code' => array(
        'text' => esc_html__('عرض الكود', 'wp-coupon'),
        'icon' => 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z',
        'color' => 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
    ),
    'sale' => array(
        'text' => esc_html__('احصل على العرض', 'wp-coupon'),
        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
        'color' => 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
    ),
    'print' => array(
        'text' => esc_html__('طباعة الكوبون', 'wp-coupon'),
        'icon' => 'M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z',
        'color' => 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'
    )
);

$action = isset($coupon_actions[$coupon_type]) ? $coupon_actions[$coupon_type] : $coupon_actions['code'];
?>

<!-- Modern Minimal Coupon Card -->
<article data-id="<?php echo $coupon_obj->ID; ?>"
         data-coupon-id="<?php echo $coupon_obj->ID; ?>"
         class="store-listing-item <?php echo esc_attr(implode(' ', $card_classes)); ?>">

    <!-- Status Badges -->
    <?php if ($has_expired) : ?>
    <div class="absolute top-3 right-3 z-20">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <?php esc_html_e('منتهي', 'wp-coupon'); ?>
        </span>
    </div>
    <?php elseif ($is_featured) : ?>
    <div class="absolute top-3 right-3 z-20">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-orange-400 to-pink-400 text-white">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            <?php esc_html_e('مميز', 'wp-coupon'); ?>
        </span>
    </div>
    <?php endif; ?>

    <!-- Card Content -->
    <div class="p-4">

        <!-- Header with Coupon Thumbnail -->
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2 space-x-reverse">
                <?php if ($has_coupon_thumb && $coupon_thumbnail) : ?>
                <div class="w-10 h-10 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                    <img src="<?php echo esc_url($coupon_thumbnail); ?>"
                         alt="<?php echo esc_attr(get_the_title($coupon_obj->ID)); ?>"
                         class="w-full h-full object-cover"
                         loading="lazy">
                </div>
                <?php else : ?>
                <!-- Show store initials as fallback -->
                <div class="w-6 h-6 rounded-md bg-gradient-to-br from-gray-400 to-gray-500 flex items-center justify-center text-white text-xs font-bold flex-shrink-0">
                    <?php echo esc_html(substr($store_name, 0, 1)); ?>
                </div>
                <?php endif; ?>
                <div class="min-w-0 flex-1">
                    <p class="text-xs mb-0 font-medium text-gray-700 truncate"><?php echo esc_html($store_name); ?></p>
                    <?php if ($success_rate > 0) : ?>
                    <p class="text-xs text-green-600"><?php printf(esc_html__('%d%% نجح', 'wp-coupon'), $success_rate); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Coupon Type Badge -->
            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium <?php echo $coupon_type === 'code' ? 'bg-blue-100 text-blue-700' : ($coupon_type === 'sale' ? 'bg-green-100 text-green-700' : 'bg-purple-100 text-purple-700'); ?>">
                <?php echo $coupon_type === 'code' ? esc_html__('كود', 'wp-coupon') : ($coupon_type === 'sale' ? esc_html__('عرض', 'wp-coupon') : esc_html__('طباعة', 'wp-coupon')); ?>
            </span>
        </div>

        <!-- Coupon Title - Clickable with distinct class -->
        <div class="coupon-title-wrapper">
            <h3 class="coupon-title text-sm font-semibold text-gray-900 mb-2 leading-tight line-clamp-2">
                <a href="<?php echo esc_url($coupon_obj->get_href()); ?>"
                   class="coupon-title-link hover:text-primary-600 transition-colors duration-200"
                   data-type="<?php echo esc_attr($coupon_type); ?>"
                   data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                   data-aff-url="<?php echo esc_attr($coupon_obj->get_destination_url()); ?>"
                   data-code="<?php echo esc_attr($coupon_code); ?>">
                    <?php echo esc_html(get_the_title($coupon_obj->ID)); ?>
                </a>
            </h3>
        </div>

        <!-- Coupon Description -->
        <p class="text-gray-600 text-xs mb-3 leading-relaxed line-clamp-2">
            <?php echo esc_html(wp_trim_words($coupon_obj->post_excerpt ?: $coupon_obj->post_content, 10)); ?>
        </p>

        <!-- Discount Display -->
        <?php if ($coupon_code && $coupon_type === 'code') : ?>
        <div class="mb-3">
            <div class="inline-flex items-center px-2 py-1 rounded-lg bg-blue-50 border border-blue-200">
                <svg class="w-3 h-3 text-blue-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-blue-800 font-mono text-xs font-semibold"><?php echo esc_html($coupon_code); ?></span>
            </div>
        </div>
        <?php elseif ($coupon_type === 'sale') : ?>
        <div class="mb-3">
            <div class="inline-flex items-center px-2 py-1 rounded-lg bg-green-50 border border-green-200">
                <svg class="w-3 h-3 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 text-xs font-semibold"><?php esc_html_e('عرض خاص', 'wp-coupon'); ?></span>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Section -->
        <div class="flex items-center justify-between pt-3 border-t border-gray-100">

            <!-- Expiry Info -->
            <div class="flex items-center text-xs text-gray-500">
                <?php if ($coupon_expires && !$has_expired) : ?>
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                <span><?php printf(esc_html__('ينتهي %s', 'wp-coupon'), date_i18n('j M', strtotime($coupon_expires))); ?></span>
                <?php endif; ?>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-2 space-x-reverse">

                <?php if ($is_featured_card) : ?>
                <!-- Save Button for Featured Cards -->
                <button data-tooltip="<?php esc_attr_e('حفظ الكوبون', 'wp-coupon'); ?>"
                        data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                        class="coupon-save p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-red-500 transition-all duration-200">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </button>
                <?php endif; ?>
                <!-- Main Action Button -->
                <div class="coupon-button-type">
                    <a data-type="<?php echo esc_attr($coupon_type); ?>"
                       data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                       data-aff-url="<?php echo esc_attr($coupon_obj->get_destination_url()); ?>"
                       data-code="<?php echo esc_attr($coupon_code); ?>"
                       class="coupon-button inline-flex items-center px-3 py-1 rounded-lg text-xs font-semibold text-white <?php echo esc_attr($action['color']); ?> hover:scale-105 transition-all duration-200 shadow-sm hover:shadow"
                       href="<?php echo esc_url($coupon_obj->get_href()); ?>">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo esc_attr($action['icon']); ?>"></path>
                        </svg>
                        <?php echo esc_html($action['text']); ?>
                    </a>
                </div>
            </div>
        </div>

        <!-- Coupon Footer with Voting and Actions -->
        <div class="coupon-footer mt-3 pt-3 border-t border-gray-100">
            <ul class="flex items-center justify-between text-xs">
                <!-- Simple Voting Section -->
                <li class="flex items-center space-x-2 space-x-reverse">
                    <div class="coupon-vote-wrapper flex items-center space-x-1 space-x-reverse">
                        <button class="coupon-vote p-1 rounded hover:bg-green-100 text-gray-400 hover:text-green-600 transition-colors duration-200"
                                data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                                data-vote-type="up">
                            👍
                        </button>
                        <button class="coupon-vote p-1 rounded hover:bg-red-100 text-gray-400 hover:text-red-600 transition-colors duration-200"
                                data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                                data-vote-type="down">
                            👎
                        </button>
                    </div>
                </li>



            </ul>

        </div>
    </div>

</article>
