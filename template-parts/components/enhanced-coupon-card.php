<?php
/**
 * Enhanced Coupon Card Component with Scratch-to-Reveal Effect
 * Modern design with comprehensive meta field integration
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get coupon object
global $wpcoupon_coupon;
if (!$wpcoupon_coupon) {
    // Try to get from global post
    global $post;
    if ($post && $post->post_type === 'coupon') {
        $wpcoupon_coupon = new WPCoupon_Coupon($post->ID);
    } else {
        echo '<div class="error-card bg-red-100 p-4 rounded">No coupon object available</div>';
        return;
    }
}

$coupon_obj = $wpcoupon_coupon;

// Get component configuration
$style = get_query_var('coupon_card_style', 'default');
$args = get_query_var('coupon_card_args', array());

// Get coupon meta data from metabox config first
$coupon_type = get_post_meta($coupon_obj->ID, '_wpc_coupon_type', true) ?: 'code';

// Default arguments
$defaults = array(
    'show_store_logo' => true,
    'show_badges' => true,
    'show_description' => true,
    'description_length' => 15,
    'scratch_effect' => ($coupon_type === 'code'), // Enable scratch for code coupons
    'hover_effects' => true
);
$args = wp_parse_args($args, $defaults);

// Continue getting coupon meta data
$coupon_code = get_post_meta($coupon_obj->ID, '_wpc_coupon_type_code', true);
$destination_url = get_post_meta($coupon_obj->ID, '_wpc_destination_url', true);
$coupon_save = get_post_meta($coupon_obj->ID, '_wpc_coupon_save', true);
$free_shipping = get_post_meta($coupon_obj->ID, '_wpc_free_shipping', true);
$is_exclusive = get_post_meta($coupon_obj->ID, '_wpc_exclusive', true);
$used_count = get_post_meta($coupon_obj->ID, '_wpc_used', true) ?: 0;
$views_count = get_post_meta($coupon_obj->ID, '_wpc_views', true) ?: 0;
$vote_up = get_post_meta($coupon_obj->ID, '_wpc_vote_up', true) ?: 0;
$vote_down = get_post_meta($coupon_obj->ID, '_wpc_vote_down', true) ?: 0;

// Get store information
$store_terms = wp_get_post_terms($coupon_obj->ID, 'coupon_store');
$store = !empty($store_terms) ? $store_terms[0] : null;
$store_image = '';
$store_name = '';
if ($store) {
    $store_image = get_term_meta($store->term_id, '_wpc_store_image', true);
    $store_name = $store->name;
}

// Get coupon featured image
$featured_image = get_the_post_thumbnail_url($coupon_obj->ID, 'medium');

// Calculate success rate
$total_votes = $vote_up + $vote_down;
$success_rate = $total_votes > 0 ? round(($vote_up / $total_votes) * 100) : 85;

// Determine if coupon has expired (using dynamic expiration)
$has_expired = false; // Simplified for now
try {
    $has_expired = $coupon_obj->has_expired();
} catch (Exception $e) {
    // Silently handle expiration check errors
    $has_expired = false;
}

// Build CSS classes based on style and state
$card_classes = array(
    'ag-coupon-card',
    'group',
    'relative',
    'bg-white',
    'rounded-2xl',
    'border',
    'border-gray-100',
    'transition-all',
    'duration-500',
    'cursor-pointer',
    'overflow-hidden'
);

// Add style-specific classes
switch ($style) {
    case 'featured':
        $card_classes[] = 'ag-coupon-featured';
        $card_classes[] = 'ring-2';
        $card_classes[] = 'ring-yellow-400';
        $card_classes[] = 'shadow-lg';
        break;
    case 'scratch':
        $card_classes[] = 'ag-coupon-scratch';
        $args['scratch_effect'] = true;
        break;
    case 'minimal':
        $card_classes[] = 'ag-coupon-minimal';
        break;
    default:
        $card_classes[] = 'ag-coupon-default';
        break;
}

// Add state classes
if ($has_expired) {
    $card_classes[] = 'ag-coupon-expired';
    $card_classes[] = 'opacity-60';
}

if ($is_exclusive) {
    $card_classes[] = 'ag-coupon-exclusive';
}

// Add hover effects
if ($args['hover_effects']) {
    $card_classes[] = 'hover:shadow-xl';
    $card_classes[] = 'hover:border-yellow-300';
    $card_classes[] = 'hover:-translate-y-2';
    $card_classes[] = 'hover:scale-105';
}

// Coupon action configuration
$coupon_actions = array(
    'code' => array(
        'text' => esc_html__('عرض الكود', 'wp-coupon'),
        'action_text' => esc_html__('انقر للحصول على الكود', 'wp-coupon'),
        'icon' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
        'color' => 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
    ),
    'deal' => array(
        'text' => esc_html__('احصل على الصفقة', 'wp-coupon'),
        'action_text' => esc_html__('انقر للحصول على الصفقة', 'wp-coupon'),
        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
        'color' => 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
    )
);

$action = isset($coupon_actions[$coupon_type]) ? $coupon_actions[$coupon_type] : $coupon_actions['code'];

// Get final destination URL
$final_url = '';
try {
    $final_url = !empty($destination_url) ? $destination_url : $coupon_obj->get_destination_url();
} catch (Exception $e) {
    $final_url = get_permalink($coupon_obj->ID);
}

// Get coupon href
$coupon_href = '';
try {
    $coupon_href = $coupon_obj->get_href();
} catch (Exception $e) {
    $coupon_href = get_permalink($coupon_obj->ID);
}
?>

<!-- Enhanced Coupon Card -->
<article data-id="<?php echo $coupon_obj->ID; ?>"
         data-coupon-id="<?php echo $coupon_obj->ID; ?>"
         data-type="<?php echo esc_attr($coupon_type); ?>"
         class="<?php echo esc_attr(implode(' ', $card_classes)); ?>">

    <!-- Animated Background Elements -->
    <div class="ag-coupon-bg absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
        <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-200 to-yellow-300 rounded-full -translate-y-16 translate-x-16 group-hover:translate-y-0 group-hover:translate-x-0 transition-transform duration-1000"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200 to-blue-300 rounded-full translate-y-12 -translate-x-12 group-hover:translate-y-0 group-hover:translate-x-0 transition-transform duration-1000 delay-200"></div>
    </div>

    <!-- Card Content -->
    <div class="relative z-10 p-6">

        <!-- Header Section -->
        <div class="flex items-start justify-between mb-4">

            <!-- Store Logo & Info -->
            <?php if ($args['show_store_logo'] && $store) : ?>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="ag-store-logo-small w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                        <?php if ($store_image) : ?>
                            <img src="<?php echo esc_url($store_image); ?>"
                                 alt="<?php echo esc_attr($store_name); ?>"
                                 class="w-full h-full object-cover">
                        <?php else : ?>
                            <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                <?php echo esc_html(substr($store_name, 0, 2)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-700"><?php echo esc_html($store_name); ?></p>
                        <div class="flex items-center space-x-1 space-x-reverse text-xs text-gray-500">
                            <span><?php printf(esc_html__('%d استخدام', 'wp-coupon'), $used_count); ?></span>
                            <span>•</span>
                            <span><?php echo esc_html($success_rate); ?>% نجاح</span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Badges -->
            <?php if ($args['show_badges']) : ?>
                <div class="flex flex-col space-y-1">
                    <?php if ($is_exclusive) : ?>
                        <span class="ag-badge-exclusive bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                            <?php esc_html_e('حصري', 'wp-coupon'); ?>
                        </span>
                    <?php endif; ?>

                    <?php if ($free_shipping) : ?>
                        <span class="ag-badge-shipping bg-gradient-to-r from-green-400 to-green-500 text-green-900 px-2 py-1 rounded-full text-xs font-bold">
                            <?php esc_html_e('شحن مجاني', 'wp-coupon'); ?>
                        </span>
                    <?php endif; ?>

                    <?php if ($coupon_save) : ?>
                        <span class="ag-badge-save bg-gradient-to-r from-red-400 to-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            <?php echo esc_html($coupon_save); ?>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Coupon Title - Clickable -->
        <div class="ag-coupon-title-wrapper mb-3">
            <h3 class="ag-coupon-title text-lg font-bold text-gray-900 leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                <a href="<?php echo esc_url($coupon_href); ?>"
                   class="ag-coupon-title-link coupon-button"
                   data-type="<?php echo esc_attr($coupon_type); ?>"
                   data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                   data-aff-url="<?php echo esc_attr($final_url); ?>"
                   data-code="<?php echo esc_attr($coupon_code); ?>">
                    <?php
                    // Force get title for this specific coupon
                    $coupon_title = get_the_title($coupon_obj->ID);
                    echo esc_html($coupon_title);
                    ?>
                </a>
            </h3>
        </div>

        <!-- Coupon Description -->
        <?php if ($args['show_description']) : ?>
            <div class="ag-coupon-description mb-4">
                <p class="text-sm text-gray-600 line-clamp-2">
                    <?php
                    // Force get description for this specific coupon
                    $coupon_post = get_post($coupon_obj->ID);
                    $description = $coupon_post->post_excerpt;
                    if (empty($description)) {
                        $description = wp_trim_words($coupon_post->post_content, $args['description_length'], '...');
                    }
                    echo esc_html($description);
                    ?>
                </p>
            </div>
        <?php endif; ?>

        <!-- Featured Image (if available) -->
        <?php if ($featured_image && $style === 'featured') : ?>
            <div class="ag-coupon-image mb-4 rounded-lg overflow-hidden">
                <img src="<?php echo esc_url($featured_image); ?>"
                     alt="<?php echo esc_attr(get_the_title($coupon_obj->ID)); ?>"
                     class="w-full h-32 object-cover group-hover:scale-110 transition-transform duration-500">
            </div>
        <?php endif; ?>

        <!-- Action Section -->
        <div class="ag-coupon-actions flex items-center justify-between">

            <!-- Voting Section -->
            <div class="ag-coupon-voting flex items-center space-x-2 space-x-reverse">
                <button class="ag-vote-up text-gray-400 hover:text-green-500 transition-colors duration-200"
                        data-coupon-id="<?php echo $coupon_obj->ID; ?>">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                <span class="text-xs text-gray-500"><?php echo esc_html($vote_up); ?></span>
                <button class="ag-vote-down text-gray-400 hover:text-red-500 transition-colors duration-200"
                        data-coupon-id="<?php echo $coupon_obj->ID; ?>">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                <span class="text-xs text-gray-500"><?php echo esc_html($vote_down); ?></span>
            </div>

            <!-- Main Action Button with Scratch Effect -->
            <div class="ag-coupon-button-wrapper relative">


                <!-- Dynamic Button Based on Coupon Type -->
                <?php if ($coupon_type === 'code' && !empty($coupon_code)) : ?>
                    <!-- Coupon Code Display Button (scratch coupon style) -->
                    <a class="ag-coupon-code-display coupon-button"
                       data-type="<?php echo esc_attr($coupon_type); ?>"
                       data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                       data-aff-url="<?php echo esc_attr($final_url); ?>"
                       data-code="<?php echo esc_attr($coupon_code); ?>"
                       href="<?php echo esc_url($coupon_href); ?>">
                        <div class="flex flex-col text-left">
                            <span class="coupon-action-text"><?php echo esc_html($action['action_text']); ?></span>
                            <span class="coupon-code-text"><?php echo esc_html($coupon_code); ?></span>
                        </div>
                        <div class="text-right flex-shrink-0">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </a>

                <?php elseif ($coupon_type === 'deal') : ?>
                    <!-- Deal Button -->
                    <a class="ag-deal-button coupon-button"
                       data-type="<?php echo esc_attr($coupon_type); ?>"
                       data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                       data-aff-url="<?php echo esc_attr($final_url); ?>"
                       data-code="<?php echo esc_attr($coupon_code); ?>"
                       href="<?php echo esc_url($coupon_href); ?>">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo esc_attr($action['icon']); ?>"></path>
                        </svg>
                        <?php echo esc_html($action['text']); ?>
                    </a>

                <?php else : ?>
                    <!-- Fallback Button for other types -->
                    <a class="ag-coupon-button coupon-button bg-gradient-to-r <?php echo esc_attr($action['color']); ?> text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg inline-flex items-center"
                       data-type="<?php echo esc_attr($coupon_type); ?>"
                       data-coupon-id="<?php echo $coupon_obj->ID; ?>"
                       data-aff-url="<?php echo esc_attr($final_url); ?>"
                       data-code="<?php echo esc_attr($coupon_code); ?>"
                       href="<?php echo esc_url($coupon_href); ?>">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo esc_attr($action['icon']); ?>"></path>
                        </svg>
                        <?php echo esc_html($action['text']); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Expiration Info -->
        <div class="ag-coupon-expiry mt-3 pt-3 border-t border-gray-100">
            <p class="text-xs text-gray-500 flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <?php
                if ($has_expired) {
                    esc_html_e('انتهت صلاحية الكوبون', 'wp-coupon');
                } else {
                    try {
                        $expires = $coupon_obj->get_expires();
                        printf(esc_html__('صالح حتى %s', 'wp-coupon'), $expires);
                    } catch (Exception $e) {
                        esc_html_e('صالح', 'wp-coupon');
                    }
                }
                ?>
            </p>
        </div>
    </div>
</article>
