<?php
global $post;
?>
<!-- Tailwind CSS Store Cards Grid -->
<div class="search-stores bg-white rounded-xl shadow-coupon p-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <?php
        while ( have_posts() ) {
            the_post();
            wpcoupon_setup_store( $post );
            $store_coupons_count = wpcoupon_store()->count;
            $is_featured = get_term_meta( wpcoupon_store()->term_id, '_wpc_is_featured', true );
            ?>
            <div class="store-card group relative overflow-hidden transition-all duration-300 hover:shadow-coupon-hover <?php echo $is_featured ? 'ring-2 ring-primary-400' : ''; ?>">

                <?php if ( $is_featured ) { ?>
                    <!-- Featured Badge -->
                    <div class="absolute top-2 right-2 z-10">
                        <span class="bg-primary-300 text-primary-900 text-xs px-2 py-1 rounded-full font-semibold">
                            <?php esc_html_e( 'مميز', 'wp-coupon' ); ?>
                        </span>
                    </div>
                <?php } ?>

                <!-- Store Thumbnail -->
                <div class="store-card-header p-4 bg-gradient-to-br from-gray-50 to-gray-100">
                    <a href="<?php echo get_permalink($post); ?>" class="block">
                        <div class="store-logo w-20 h-20 mx-auto mb-3 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow duration-200">
                            <?php echo wpcoupon_store()->get_thumbnail() ?>
                        </div>
                    </a>
                </div>

                <!-- Store Information -->
                <div class="store-card-body p-4">
                    <h3 class="store-name text-center">
                        <a href="<?php echo get_permalink($post); ?>"
                           class="text-lg font-bold text-gray-900 hover:text-secondary-600 transition-colors duration-200 line-clamp-2">
                            <?php the_title(); ?>
                        </a>
                    </h3>

                    <!-- Store Stats -->
                    <div class="mt-3 text-center">
                        <div class="inline-flex items-center bg-secondary-50 text-secondary-700 px-3 py-1 rounded-full text-sm">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 18a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="font-medium"><?php echo $store_coupons_count; ?> <?php esc_html_e( 'كوبون', 'wp-coupon' ); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Store Action -->
                <div class="store-card-footer p-4 pt-0">
                    <a href="<?php echo get_permalink($post); ?>"
                       class="btn-outline w-full text-center py-2 px-4 rounded-lg font-semibold transition-all duration-200 hover:transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-300">
                        <?php esc_html_e( 'عرض الكوبونات', 'wp-coupon' ); ?>
                        <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                </div>
            </div>
        <?php }  ?>
    </div>
</div>