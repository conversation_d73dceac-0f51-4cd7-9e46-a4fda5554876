<?php
/**
 * Template part for displaying content in loops
 * Uses enhanced coupon card system
 *
 * @package Ag-Coupon
 */

while (have_posts()) {
    the_post();
    if ('coupon' == get_post_type()) {
        wpcoupon_setup_coupon($post);

        // Use enhanced coupon card system
        echo '<div class="coupon-grid-item">';
        if (function_exists('render_enhanced_coupon_card')) {
            render_enhanced_coupon_card($post, 'default', array(
                'section' => 'content-loop',
                'track_display' => true,
                'show_store_logo' => true,
                'show_badges' => true,
                'show_description' => true,
                'description_length' => 15,
                'scratch_effect' => false,
                'hover_effects' => true
            ));
        } else {
            // Fallback to helper function
            wpcoupon_render_coupon_card($post, false);
        }
        echo '</div>';
    } else {
        get_template_part('template-parts/loop/loop');
    }
}
