<?php
/**
 * Template part for displaying content in loops
 * Uses enhanced coupon card system
 *
 * @package Ag-Coupon
 */

while (have_posts()) {
    the_post();
    if ('coupon' == get_post_type()) {
        // Use unified coupon card system
        echo '<div class="coupon-grid-item">';
        wpcoupon_render_coupon_card($post, 'default', array(
            'show_store_logo' => true,
            'show_badges' => true,
            'show_description' => true,
            'description_length' => 15,
            'hover_effects' => true,
            'show_image' => true
        ));
        echo '</div>';
    } else {
        get_template_part('template-parts/loop/loop');
    }
}
