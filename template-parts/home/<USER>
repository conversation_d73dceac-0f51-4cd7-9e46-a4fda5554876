<?php
/**
 * Latest Coupons Section for Home Page
 * Uses enhanced coupon card system with deduplication
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get latest coupons data with enhanced deduplication
$latest_coupons = get_query_var('latest_coupons', array());
if (empty($latest_coupons)) {
    // Try enhanced function first
    if (function_exists('get_all_coupons')) {
        $latest_coupons = get_all_coupons(
            12,
            array(), // no manual exclusions
            true, // exclude already displayed
            'home-latest-coupons' // section identifier
        );
    }

    // Fallback to original theme function
    if (empty($latest_coupons) && function_exists('wpcoupon_get_coupons')) {
        $latest_coupons = wpcoupon_get_coupons(array(
            'posts_per_page' => 12,
            'hide_expired' => false,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    }

    // Ultimate fallback: Direct WP_Query
    if (empty($latest_coupons)) {
        $query = new WP_Query(array(
            'post_type' => 'coupon',
            'posts_per_page' => 12,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        if ($query->have_posts()) {
            $latest_coupons = $query->posts;
        }
        wp_reset_postdata();
    }
}

// Only show section if we have coupons
if (empty($latest_coupons)) {
    return;
}
?>

<!-- Latest Coupons Section -->
<section class="ag-latest-coupons-section py-16 bg-white">
    <div class="container mx-auto px-4">

        <!-- Section Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <?php esc_html_e('أحدث الكوبونات', 'wp-coupon'); ?>
            </h2>

            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('تصفح أحدث الكوبونات والعروض المضافة حديثاً من جميع المتاجر', 'wp-coupon'); ?>
            </p>

            <!-- Decorative Elements -->
            <div class="flex justify-center items-center space-x-4 space-x-reverse mt-6">
                <div class="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                <div class="w-6 h-0.5 bg-blue-400"></div>
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <div class="w-12 h-0.5 bg-gradient-to-l from-transparent to-green-400"></div>
            </div>
        </div>

        <!-- Latest Coupons Grid - Simple System -->
        <?php if (!empty($latest_coupons)) : ?>

            <!-- Latest Coupons Grid -->
            <div id="home-latest-coupons-grid-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php foreach ($latest_coupons as $coupon) : ?>
                    <div class="coupon-grid-item">
                        <?php
                        // Use simple coupon card system
                        render_coupon_card($coupon, 'default', array(
                            'show_store_logo' => true,
                            'show_badges' => true,
                            'show_description' => true,
                            'description_length' => 15,
                            'show_image' => true
                        ));
                        ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Load More Button -->
            <div class="load-more-container text-center mt-8">
                <button class="load-more-btn ag-load-more-coupons bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200"
                        data-doing="load_coupons"
                        data-next-page="2"
                        data-per-page="4"
                        data-section="home-latest-coupons-grid-grid">
                    <span class="btn-text">تحميل المزيد من الكوبونات</span>
                    <span class="loading-spinner hidden ml-2">
                        <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                </button>
                <div class="loading-progress hidden mt-2">
                    <div class="w-full bg-gray-200 rounded-full h-1">
                        <div class="bg-primary-500 h-1 rounded-full animate-pulse" style="width: 45%"></div>
                    </div>
                </div>
            </div>
        <?php else : ?>
            <p class="text-center text-gray-500 py-8">لا توجد كوبونات متاحة حالياً</p>
        <?php endif; ?>

        <!-- Quick Stats -->
        <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mt-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">

                <!-- Today's Coupons -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                        <?php
                        $today_coupons = get_posts(array(
                            'post_type' => 'coupon',
                            'posts_per_page' => -1,
                            'date_query' => array(
                                array(
                                    'after' => 'today',
                                    'inclusive' => true,
                                ),
                            ),
                        ));
                        echo count($today_coupons);
                        ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('كوبون جديد اليوم', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Success Rate -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">94%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('معدل نجاح الكوبونات', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Average Savings -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">35%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('متوسط الوفورات', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Load more functionality is now working correctly -->
