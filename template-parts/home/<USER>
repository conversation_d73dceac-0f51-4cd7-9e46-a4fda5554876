<?php
/**
 * Latest Coupons Section for Home Page
 * Uses enhanced coupon card system with deduplication
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get latest coupons data with deduplication
$latest_coupons = get_query_var('latest_coupons', array());
if (empty($latest_coupons)) {
    $latest_coupons = get_all_coupons(
        12,
        array(), // no manual exclusions
        true, // exclude already displayed
        'home-latest-coupons' // section identifier
    );
}

// Debug: Check if we have coupons
if (WP_DEBUG) {
    error_log('Latest Coupons Count: ' . count($latest_coupons));
    if (!empty($latest_coupons)) {
        error_log('First Coupon ID: ' . $latest_coupons[0]->ID);
        error_log('First Coupon Title: ' . get_the_title($latest_coupons[0]->ID));
    }
}

// Fallback: Try to get coupons using the original method if enhanced method fails
if (empty($latest_coupons)) {
    $latest_coupons = wpcoupon_get_coupons(array(
        'posts_per_page' => 12,
        'hide_expired' => true,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
}

// Ultimate fallback: Direct WP_Query
if (empty($latest_coupons)) {
    $query = new WP_Query(array(
        'post_type' => 'coupon',
        'posts_per_page' => 12,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    if ($query->have_posts()) {
        $latest_coupons = $query->posts;
    }
    wp_reset_postdata();
}

// Only show section if we have coupons
if (empty($latest_coupons)) {
    if (WP_DEBUG) {
        echo '<div class="text-center py-8"><p>Debug: No coupons found in database</p></div>';
    }
    return;
}
?>

<!-- Latest Coupons Section -->
<section class="ag-latest-coupons-section py-16 bg-white">
    <div class="container mx-auto px-4">

        <!-- Section Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <?php esc_html_e('أحدث الكوبونات', 'wp-coupon'); ?>
            </h2>

            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('تصفح أحدث الكوبونات والعروض المضافة حديثاً من جميع المتاجر', 'wp-coupon'); ?>
            </p>

            <!-- Decorative Elements -->
            <div class="flex justify-center items-center space-x-4 space-x-reverse mt-6">
                <div class="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                <div class="w-6 h-0.5 bg-blue-400"></div>
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <div class="w-12 h-0.5 bg-gradient-to-l from-transparent to-green-400"></div>
            </div>
        </div>

        <!-- Latest Coupons Grid -->
        <?php if (function_exists('render_coupons_grid')) : ?>
            <?php
            render_coupons_grid($latest_coupons, 'default', array(
                'columns' => 4,
                'columns_tablet' => 2,
                'columns_mobile' => 1,
                'gap' => 6,
                'container_class' => 'latest-coupons-grid-container mb-8',
                'section' => 'home-latest-coupons-grid',
                'track_coupons' => true,
                'show_load_more' => true,
                'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon')
            ));
            ?>
        <?php else : ?>
            <!-- Fallback: Use existing coupon rendering -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php foreach ($latest_coupons as $coupon) : ?>
                    <div class="coupon-grid-item">
                        <?php
                        // Set up coupon context for existing template
                        wpcoupon_setup_coupon($coupon);

                        // Use existing coupon card template
                        if (function_exists('wpcoupon_render_coupon_card')) {
                            wpcoupon_render_coupon_card($coupon, false);
                        } else {
                            // Ultimate fallback: basic coupon display
                            ?>
                            <div class="bg-white rounded-lg border p-4">
                                <h3 class="font-bold text-lg mb-2"><?php echo esc_html(get_the_title($coupon->ID)); ?></h3>
                                <p class="text-gray-600 text-sm mb-4"><?php echo esc_html(wp_trim_words(get_the_excerpt($coupon->ID), 15)); ?></p>
                                <a href="<?php echo esc_url(get_permalink($coupon->ID)); ?>" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                    <?php esc_html_e('عرض الكوبون', 'wp-coupon'); ?>
                                </a>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Quick Stats -->
        <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mt-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">

                <!-- Today's Coupons -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                        <?php
                        $today_coupons = get_posts(array(
                            'post_type' => 'coupon',
                            'posts_per_page' => -1,
                            'date_query' => array(
                                array(
                                    'after' => 'today',
                                    'inclusive' => true,
                                ),
                            ),
                        ));
                        echo count($today_coupons);
                        ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('كوبون جديد اليوم', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Success Rate -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">94%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('معدل نجاح الكوبونات', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Average Savings -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">35%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('متوسط الوفورات', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
