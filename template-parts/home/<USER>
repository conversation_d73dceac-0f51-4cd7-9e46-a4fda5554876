<?php
/**
 * Latest Coupons Section for Home Page
 * Uses enhanced coupon card system with deduplication
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get latest coupons data with enhanced deduplication
$latest_coupons = get_query_var('latest_coupons', array());
if (empty($latest_coupons)) {
    // Try enhanced function first
    if (function_exists('get_all_coupons')) {
        $latest_coupons = get_all_coupons(
            12,
            array(), // no manual exclusions
            true, // exclude already displayed
            'home-latest-coupons' // section identifier
        );
    }

    // Fallback to original theme function
    if (empty($latest_coupons) && function_exists('wpcoupon_get_coupons')) {
        $latest_coupons = wpcoupon_get_coupons(array(
            'posts_per_page' => 12,
            'hide_expired' => false,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    }

    // Ultimate fallback: Direct WP_Query
    if (empty($latest_coupons)) {
        $query = new WP_Query(array(
            'post_type' => 'coupon',
            'posts_per_page' => 12,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        if ($query->have_posts()) {
            $latest_coupons = $query->posts;
        }
        wp_reset_postdata();
    }
}

// Only show section if we have coupons
if (empty($latest_coupons)) {
    return;
}
?>

<!-- Latest Coupons Section -->
<section class="ag-latest-coupons-section py-16 bg-white">
    <div class="container mx-auto px-4">

        <!-- Section Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <?php esc_html_e('أحدث الكوبونات', 'wp-coupon'); ?>
            </h2>

            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('تصفح أحدث الكوبونات والعروض المضافة حديثاً من جميع المتاجر', 'wp-coupon'); ?>
            </p>

            <!-- Decorative Elements -->
            <div class="flex justify-center items-center space-x-4 space-x-reverse mt-6">
                <div class="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                <div class="w-6 h-0.5 bg-blue-400"></div>
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <div class="w-12 h-0.5 bg-gradient-to-l from-transparent to-green-400"></div>
            </div>
        </div>

        <!-- Latest Coupons Grid -->
        <?php if (function_exists('render_coupons_grid')) : ?>
            <?php
            render_coupons_grid($latest_coupons, 'default', array(
                'columns' => 4,
                'columns_tablet' => 2,
                'columns_mobile' => 1,
                'gap' => 6,
                'container_class' => 'latest-coupons-grid-container mb-8',
                'section' => 'home-latest-coupons-grid',
                'track_coupons' => true,
                'show_load_more' => true,
                'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon')
            ));
            ?>
        <?php else : ?>
            <!-- Fallback: Manual grid rendering -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php foreach ($latest_coupons as $coupon) : ?>
                    <div class="coupon-grid-item">
                        <?php
                        // Try enhanced rendering first
                        if (function_exists('render_enhanced_coupon_card')) {
                            render_enhanced_coupon_card($coupon, 'default', array(
                                'section' => 'home-latest-coupons-grid',
                                'track_display' => true
                            ));
                        }
                        // Try existing theme function
                        elseif (function_exists('wpcoupon_render_coupon_card')) {
                            wpcoupon_setup_coupon($coupon);
                            wpcoupon_render_coupon_card($coupon, false);
                        }
                        // Ultimate fallback: basic coupon display
                        else {
                            ?>
                            <div class="bg-white rounded-lg border p-4 shadow-sm hover:shadow-md transition-shadow">
                                <h3 class="font-bold text-lg mb-2 text-gray-900"><?php echo esc_html(get_the_title($coupon->ID)); ?></h3>
                                <p class="text-gray-600 text-sm mb-4"><?php echo esc_html(wp_trim_words(get_the_excerpt($coupon->ID), 15)); ?></p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">ID: <?php echo $coupon->ID; ?></span>
                                    <a href="<?php echo esc_url(get_permalink($coupon->ID)); ?>" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                        <?php esc_html_e('عرض الكوبون', 'wp-coupon'); ?>
                                    </a>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Quick Stats -->
        <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mt-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">

                <!-- Today's Coupons -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                        <?php
                        $today_coupons = get_posts(array(
                            'post_type' => 'coupon',
                            'posts_per_page' => -1,
                            'date_query' => array(
                                array(
                                    'after' => 'today',
                                    'inclusive' => true,
                                ),
                            ),
                        ));
                        echo count($today_coupons);
                        ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('كوبون جديد اليوم', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Success Rate -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">94%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('معدل نجاح الكوبونات', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Average Savings -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">35%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('متوسط الوفورات', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Temporary Debug Panel for Load More -->
<div id="debug-panel" style="position: fixed; top: 10px; right: 10px; background: white; border: 2px solid #007cba; border-radius: 8px; padding: 15px; max-width: 400px; z-index: 9999; font-family: monospace; font-size: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
    <div style="font-weight: bold; margin-bottom: 10px; color: #007cba;">🔧 Load More Debug Panel</div>
    <div id="debug-results">Loading...</div>
    <button onclick="testLoadMoreManual()" style="background: #007cba; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 10px; cursor: pointer;">Test Load More</button>
    <button onclick="testAjaxDirect()" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 10px; margin-left: 5px; cursor: pointer;">Test AJAX</button>
    <button onclick="closeDebugPanel()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; margin-top: 10px; margin-left: 5px; cursor: pointer;">Close</button>
</div>

<script>
function addDebugResult(message, type = 'info') {
    const resultsDiv = document.getElementById('debug-results');
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };

    resultsDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 2px 0;">${message}</div>`;
    console.log(message);
}

function closeDebugPanel() {
    document.getElementById('debug-panel').style.display = 'none';
}

function testLoadMoreManual() {
    addDebugResult('🖱️ Manual test started...', 'info');
    const $btn = jQuery('.load-more-btn').first();
    if ($btn.length) {
        const initialCount = jQuery('#latest-coupons-grid').children().length;
        addDebugResult(`Initial count: ${initialCount}`, 'info');
        addDebugResult(`Button classes: ${$btn.attr('class')}`, 'info');
        addDebugResult(`Button data: ${JSON.stringify($btn.data())}`, 'info');

        // Check if AgCoupon is initialized
        if (typeof window.AgCoupon !== 'undefined') {
            addDebugResult(`AgCoupon initialized: ${window.AgCoupon.initialized}`, 'info');
            addDebugResult(`AgCoupon AJAX URL: ${window.AgCoupon.ajax_url}`, 'info');
        } else {
            addDebugResult('❌ AgCoupon not found', 'error');
        }

        // Check if event handlers are attached
        const events = jQuery._data($btn[0], 'events');
        addDebugResult(`Button events: ${events ? Object.keys(events).join(', ') : 'No events'}`, 'info');

        // Monitor console for debugging output
        addDebugResult('Watch browser console for detailed debugging...', 'info');

        // Try to trigger the click
        addDebugResult('Triggering button click...', 'info');
        $btn.trigger('click');

        setTimeout(function() {
            const finalCount = jQuery('#latest-coupons-grid').children().length;
            addDebugResult(`Final count: ${finalCount}`, 'info');
            if (finalCount > initialCount) {
                addDebugResult(`✅ Success! ${finalCount - initialCount} new items`, 'success');
            } else {
                addDebugResult(`❌ Failed - no new items added`, 'error');

                // Additional debugging
                addDebugResult('Checking for loading state...', 'info');
                if ($btn.hasClass('loading')) {
                    addDebugResult('Button is in loading state', 'warning');
                } else {
                    addDebugResult('Button is not in loading state', 'info');
                }

                // Check if console shows any errors
                addDebugResult('Check browser console for JavaScript errors', 'warning');
            }
        }, 5000); // Increased timeout to 5 seconds
    } else {
        addDebugResult('❌ No button found', 'error');
    }
}

function testAjaxDirect() {
    addDebugResult('🧪 Direct AJAX test...', 'info');
    if (typeof ST === 'undefined') {
        addDebugResult('❌ ST object not found', 'error');
        return;
    }

    jQuery.ajax({
        url: ST.ajax_url,
        type: 'POST',
        data: {
            action: 'wpcoupon_coupon_ajax',
            st_doing: 'load_coupons',
            next_page: 2,
            _wpnonce: ST._wpnonce,
            args: {
                layout: '',
                posts_per_page: '4',
                num_words: '',
                hide_expired: ''
            }
        },
        success: function(response) {
            if (response.success && response.data && response.data.content) {
                addDebugResult(`✅ AJAX Success! ${response.data.content.length} chars`, 'success');
                addDebugResult(`Next: ${response.data.next_page}/${response.data.max_pages}`, 'info');

                // Try to append content manually
                const $newContent = jQuery(response.data.content);
                const $container = jQuery('#latest-coupons-grid');
                if ($container.length && $newContent.length) {
                    $newContent.hide().appendTo($container).fadeIn(300);
                    addDebugResult(`✅ Content appended manually!`, 'success');
                }
            } else {
                addDebugResult('❌ AJAX: No content', 'error');
            }
        },
        error: function(xhr, status, error) {
            addDebugResult(`❌ AJAX failed: ${error}`, 'error');
        }
    });
}

jQuery(document).ready(function($) {
    addDebugResult('🔧 Home page loaded', 'info');

    setTimeout(function() {
        addDebugResult('🔧 Running diagnostics...', 'info');

        // Check environment
        if (typeof ST !== 'undefined') {
            addDebugResult('✅ ST object found', 'success');
            addDebugResult(`AJAX URL: ${ST.ajax_url}`, 'info');
        } else {
            addDebugResult('❌ ST object missing', 'error');
        }

        if (typeof window.AgCoupon !== 'undefined') {
            addDebugResult('✅ AgCoupon found', 'success');
        } else {
            addDebugResult('❌ AgCoupon missing', 'error');
        }

        // Check buttons
        const $buttons = $('.load-more-btn');
        if ($buttons.length > 0) {
            addDebugResult(`✅ ${$buttons.length} load more button(s)`, 'success');
            const $btn = $buttons.first();
            addDebugResult(`Button text: "${$btn.find('.btn-text').text().trim()}"`, 'info');
        } else {
            addDebugResult('❌ No load more buttons', 'error');
        }

        // Check container
        const $container = $('#latest-coupons-grid');
        if ($container.length > 0) {
            addDebugResult(`✅ Container found (${$container.children().length} items)`, 'success');
        } else {
            addDebugResult('❌ Container missing', 'error');
        }

        // Test AJAX
        if (typeof ST !== 'undefined' && ST.ajax_url) {
            addDebugResult('🧪 Testing AJAX...', 'info');
            $.ajax({
                url: ST.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpcoupon_coupon_ajax',
                    st_doing: 'load_coupons',
                    next_page: 2,
                    _wpnonce: ST._wpnonce,
                    args: {
                        layout: '',
                        posts_per_page: '4',
                        num_words: '',
                        hide_expired: ''
                    }
                },
                success: function(response) {
                    if (response.success && response.data && response.data.content) {
                        addDebugResult(`✅ AJAX works! ${response.data.content.length} chars`, 'success');
                        addDebugResult(`Next: ${response.data.next_page}/${response.data.max_pages}`, 'info');
                    } else {
                        addDebugResult('❌ AJAX: No content', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    addDebugResult(`❌ AJAX failed: ${error}`, 'error');
                }
            });
        }

        addDebugResult('🔧 Diagnostics complete', 'info');
    }, 2000);
});
</script>
