<?php
/**
 * Home Page Featured Stores Section - Slider Version
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get featured stores data
$featured_stores = get_query_var('featured_stores', array());
if (empty($featured_stores)) {
    $featured_stores = get_featured_stores(12);
}
?>

<!-- Featured Stores Slider Section -->
<section class="featured-stores-section py-16 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="section-header text-center mb-12">
            <h2 class="section-title text-3xl lg:text-4xl font-bold text-gray-900 mb-4 relative">
                <?php esc_html_e('المتاجر المميزة', 'wp-coupon'); ?>
                <span class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full"></span>
            </h2>
            <p class="section-subtitle text-lg text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('اكتشف أفضل المتاجر والعلامات التجارية المميزة مع أحدث العروض والخصومات الحصرية', 'wp-coupon'); ?>
            </p>
        </div>

        <?php if (!empty($featured_stores)) : ?>
            <!-- Featured Stores Slider -->
            <?php
            render_featured_stores_slider($featured_stores, array(
                'slides_per_view' => 4,
                'slides_per_view_mobile' => 1,
                'slides_per_view_tablet' => 2,
                'gap' => 24,
                'autoplay' => true,
                'autoplay_delay' => 4000,
                'loop' => true,
                'pagination' => true,
                'navigation' => true
            ));
            ?>

            <!-- View All Stores Button -->
            <div class="section-footer text-center mt-12">
                <a href="<?php echo esc_url(home_url('/stores/')); ?>"
                   class="view-all-btn inline-flex items-center gap-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl group">
                    <?php esc_html_e('عرض جميع المتاجر', 'wp-coupon'); ?>
                    <svg class="btn-arrow w-5 h-5 transition-transform duration-300 group-hover:translate-x-1 rtl:group-hover:-translate-x-1"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        <?php else : ?>
            <!-- No Stores Message -->
            <div class="no-stores-message text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                        <?php esc_html_e('لا توجد متاجر مميزة حالياً', 'wp-coupon'); ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('سيتم عرض المتاجر المميزة هنا عندما تصبح متاحة', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
