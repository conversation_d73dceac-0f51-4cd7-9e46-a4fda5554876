<?php
/**
 * Latest Coupons Section for Home Page
 * Uses enhanced coupon card system with deduplication
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Debug: Always show debug info for now
echo '<div class="debug-info bg-yellow-100 p-4 mb-4 rounded">';
echo '<h4>Debug Information:</h4>';

// Check if enhanced functions exist
echo '<p>Enhanced functions loaded: ' . (function_exists('get_all_coupons') ? 'YES' : 'NO') . '</p>';
echo '<p>Render function exists: ' . (function_exists('render_coupons_grid') ? 'YES' : 'NO') . '</p>';

// Try to get coupons using different methods
$latest_coupons = array();

// Method 1: Enhanced function
if (function_exists('get_all_coupons')) {
    $latest_coupons = get_all_coupons(12, array(), false, 'home-latest-coupons');
    echo '<p>Enhanced method returned: ' . count($latest_coupons) . ' coupons</p>';
}

// Method 2: Original theme function
if (empty($latest_coupons) && function_exists('wpcoupon_get_coupons')) {
    $latest_coupons = wpcoupon_get_coupons(array(
        'posts_per_page' => 12,
        'hide_expired' => false,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    echo '<p>Original theme method returned: ' . count($latest_coupons) . ' coupons</p>';
}

// Method 3: Direct WP_Query
if (empty($latest_coupons)) {
    $query = new WP_Query(array(
        'post_type' => 'coupon',
        'posts_per_page' => 12,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    if ($query->have_posts()) {
        $latest_coupons = $query->posts;
        echo '<p>Direct WP_Query returned: ' . count($latest_coupons) . ' coupons</p>';
    } else {
        echo '<p>Direct WP_Query returned: 0 coupons</p>';
    }
    wp_reset_postdata();
}

// Check total coupons in database
$total_coupons = wp_count_posts('coupon');
echo '<p>Total coupons in database: ' . $total_coupons->publish . ' published, ' . $total_coupons->draft . ' draft</p>';

// Show first coupon details if available
if (!empty($latest_coupons)) {
    $first_coupon = $latest_coupons[0];
    echo '<p>First coupon ID: ' . $first_coupon->ID . '</p>';
    echo '<p>First coupon title: ' . get_the_title($first_coupon->ID) . '</p>';
    echo '<p>First coupon type: ' . $first_coupon->post_type . '</p>';
}

echo '</div>';

// Only show section if we have coupons
if (empty($latest_coupons)) {
    echo '<div class="text-center py-8"><p class="text-red-500">No coupons found to display</p></div>';
    return;
}
?>

<!-- Latest Coupons Section -->
<section class="ag-latest-coupons-section py-16 bg-white">
    <div class="container mx-auto px-4">

        <!-- Section Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mb-6">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <?php esc_html_e('أحدث الكوبونات', 'wp-coupon'); ?>
            </h2>

            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('تصفح أحدث الكوبونات والعروض المضافة حديثاً من جميع المتاجر', 'wp-coupon'); ?>
            </p>

            <!-- Decorative Elements -->
            <div class="flex justify-center items-center space-x-4 space-x-reverse mt-6">
                <div class="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                <div class="w-6 h-0.5 bg-blue-400"></div>
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <div class="w-12 h-0.5 bg-gradient-to-l from-transparent to-green-400"></div>
            </div>
        </div>

        <!-- Latest Coupons Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <?php foreach ($latest_coupons as $coupon) : ?>
                <div class="coupon-grid-item">
                    <?php
                    echo '<div class="debug-coupon bg-gray-100 p-2 mb-2 text-xs">';
                    echo 'Rendering coupon ID: ' . $coupon->ID . ' - ' . get_the_title($coupon->ID);
                    echo '</div>';

                    // Try enhanced rendering first
                    if (function_exists('render_enhanced_coupon_card')) {
                        echo '<div class="enhanced-card">';
                        render_enhanced_coupon_card($coupon, 'default', array(
                            'section' => 'home-latest-coupons-grid',
                            'track_display' => false // Disable tracking for now
                        ));
                        echo '</div>';
                    }
                    // Try existing theme function
                    elseif (function_exists('wpcoupon_render_coupon_card')) {
                        echo '<div class="existing-card">';
                        wpcoupon_setup_coupon($coupon);
                        wpcoupon_render_coupon_card($coupon, false);
                        echo '</div>';
                    }
                    // Try existing template part
                    elseif (function_exists('wpcoupon_setup_coupon')) {
                        echo '<div class="template-part-card">';
                        wpcoupon_setup_coupon($coupon);
                        get_template_part('template-parts/components/coupon-card-enhanced');
                        echo '</div>';
                    }
                    // Ultimate fallback: basic coupon display
                    else {
                        ?>
                        <div class="bg-white rounded-lg border p-4 shadow-sm hover:shadow-md transition-shadow">
                            <h3 class="font-bold text-lg mb-2 text-gray-900"><?php echo esc_html(get_the_title($coupon->ID)); ?></h3>
                            <p class="text-gray-600 text-sm mb-4"><?php echo esc_html(wp_trim_words(get_the_excerpt($coupon->ID), 15)); ?></p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">ID: <?php echo $coupon->ID; ?></span>
                                <a href="<?php echo esc_url(get_permalink($coupon->ID)); ?>" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                    <?php esc_html_e('عرض الكوبون', 'wp-coupon'); ?>
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Quick Stats -->
        <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mt-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">

                <!-- Today's Coupons -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                        <?php
                        $today_coupons = get_posts(array(
                            'post_type' => 'coupon',
                            'posts_per_page' => -1,
                            'date_query' => array(
                                array(
                                    'after' => 'today',
                                    'inclusive' => true,
                                ),
                            ),
                        ));
                        echo count($today_coupons);
                        ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('كوبون جديد اليوم', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Success Rate -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">94%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('معدل نجاح الكوبونات', 'wp-coupon'); ?>
                    </p>
                </div>

                <!-- Average Savings -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">35%</h3>
                    <p class="text-gray-600">
                        <?php esc_html_e('متوسط الوفورات', 'wp-coupon'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
