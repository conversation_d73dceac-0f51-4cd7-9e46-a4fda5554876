<?php
/**
 * Home Page Popular Stores Section
 * Uses featured store card component and prevents duplicates
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get popular stores (non-featured) using simplified system
$featured_stores = get_query_var('featured_stores', array());
$featured_store_ids = array();

// Extract featured store IDs to exclude them
if (!empty($featured_stores)) {
    foreach ($featured_stores as $store) {
        if (isset($store->term_id)) {
            $featured_store_ids[] = $store->term_id;
        }
    }
}

// Get popular stores excluding featured ones
$popular_stores = get_all_stores(12, $featured_store_ids);
?>

<!-- Popular Stores Section -->
<section class="popular-stores-section py-16 bg-gradient-to-br from-gray-50 via-white to-blue-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="section-header text-center mb-12">
            <h2 class="section-title text-3xl lg:text-4xl font-bold text-gray-900 mb-4 relative">
                <?php esc_html_e('المتاجر الأكثر شعبية', 'wp-coupon'); ?>
                <span class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full"></span>
            </h2>
            <p class="section-subtitle text-lg text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('تسوق من المتاجر المفضلة لدى المستخدمين واحصل على أفضل العروض والخصومات', 'wp-coupon'); ?>
            </p>
        </div>

        <?php if (!empty($popular_stores)) : ?>
            <!-- Popular Stores Grid -->
            <?php
            render_stores_grid($popular_stores, 'grid', array(
                'columns' => 4,
                'columns_tablet' => 2,
                'columns_mobile' => 1,
                'gap' => 6,
                'container_class' => 'popular-stores-grid-container mb-12'
            ));
            ?>

        <!-- View All Stores Button -->
        <div class="section-footer text-center">
            <?php
            // Get a proper stores archive URL
            $stores_url = '';

            // Try to get the stores taxonomy archive URL
            $stores_taxonomy_link = get_term_link('', 'coupon_store');
            if (!is_wp_error($stores_taxonomy_link)) {
                $stores_url = $stores_taxonomy_link;
            } else {
                // Fallback: Try to get any store term and use its link
                $store_terms = get_terms(array(
                    'taxonomy' => 'coupon_store',
                    'number' => 1,
                    'hide_empty' => false
                ));

                if (!empty($store_terms) && !is_wp_error($store_terms)) {
                    $first_store_link = get_term_link($store_terms[0]);
                    if (!is_wp_error($first_store_link)) {
                        // Get the base URL without the specific store slug
                        $stores_url = dirname($first_store_link) . '/';
                    }
                }

                // Final fallback: Use home URL with stores path
                if (empty($stores_url)) {
                    $stores_url = home_url('/stores/');
                }
            }
            ?>
            <a href="<?php echo esc_url($stores_url); ?>"
               class="view-all-btn inline-flex items-center gap-3 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl group">
                <?php esc_html_e('عرض جميع المتاجر', 'wp-coupon'); ?>
                <svg class="btn-arrow w-5 h-5 transition-transform duration-300 group-hover:translate-x-1 rtl:group-hover:-translate-x-1"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
        <?php else : ?>
        <!-- No Stores Message -->
        <div class="no-stores-message text-center py-16">
            <div class="max-w-md mx-auto">
                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                    <?php esc_html_e('لا توجد متاجر شعبية حالياً', 'wp-coupon'); ?>
                </h3>
                <p class="text-gray-600">
                    <?php esc_html_e('سيتم عرض المتاجر الأكثر شعبية هنا عندما تصبح متاحة', 'wp-coupon'); ?>
                </p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php
// Simple debug information
if (WP_DEBUG && current_user_can('manage_options')) {
    echo '<!-- Popular Stores Section Debug:';
    echo ' Popular Stores Found: ' . count($popular_stores);
    echo ' -->';
}
?>
