<?php
/**
 * Home Page Latest Coupons Section
 *
 * @package Ag-Coupon
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get latest coupons with pagination support using the same method as wpcoupon_get_coupons()
$per_page = 8;
$current_page = 1;

// Use the same query structure as the original wpcoupon_get_coupons function
$coupon_args = array(
    'posts_per_page' => $per_page,
    'hide_expired' => true,  // Hide expired coupons
    'orderby' => 'date',     // Order by post date (not meta)
    'order' => 'DESC'
);

// Get coupons using the original function for consistency
$max_pages = 0;
$latest_coupons = wpcoupon_get_coupons($coupon_args, $current_page, $max_pages);

// Fallback: if no coupons found with hide_expired, try without it
if (empty($latest_coupons)) {
    $coupon_args['hide_expired'] = false;
    $latest_coupons = wpcoupon_get_coupons($coupon_args, $current_page, $max_pages);
}

// Final fallback: simple query if still no coupons
if (empty($latest_coupons)) {
    $simple_args = array(
        'posts_per_page' => $per_page,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    $latest_coupons = wpcoupon_get_coupons($simple_args, $current_page, $max_pages);
}

// Ultimate fallback: direct WP_Query if wpcoupon_get_coupons fails
if (empty($latest_coupons)) {
    $direct_query = new WP_Query(array(
        'post_type' => 'coupon',
        'posts_per_page' => $per_page,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    if ($direct_query->have_posts()) {
        $latest_coupons = $direct_query->posts;
        $max_pages = $direct_query->max_num_pages;
    }
    wp_reset_postdata();
}

// Create pagination data manually since we're using the original function
$pagination_data = array(
    'current_page' => $current_page,
    'max_pages' => $max_pages,
    'next_page' => ($current_page < $max_pages) ? $current_page + 1 : 0,
    'has_more' => $current_page < $max_pages,
    'total_posts' => count($latest_coupons) * $max_pages, // Approximate
    'posts_per_page' => $per_page
);

// Production ready - debug information removed
?>

<!-- Latest Coupons Section -->
<section class="latest-coupons-section">
    <div class="section-container">
        <div class="section-header">
            <h2 class="section-title"><?php esc_html_e('أحدث الكوبونات', 'wp-coupon'); ?></h2>
            <p class="section-subtitle"><?php esc_html_e('اكتشف أحدث كوبونات الخصم والعروض الحصرية', 'wp-coupon'); ?></p>
        </div>

        <?php if (!empty($latest_coupons)) : ?>
            <?php
            // Use the new integrated rendering system
            wpcoupon_render_coupons_with_load_more($latest_coupons, array(
                'container_id' => 'latest-coupons-grid',
                'container_class' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4',
                'show_load_more' => $pagination_data['has_more'],
                'load_more_text' => esc_html__('تحميل المزيد من الكوبونات', 'wp-coupon'),
                'loading_text' => esc_html__('جاري التحميل...', 'wp-coupon'),
                'doing' => 'load_coupons',
                'next_page' => $pagination_data['next_page'],
                'max_pages' => $pagination_data['max_pages'],
                'current_page' => $pagination_data['current_page'],
                'preset' => 'modern',
                'featured_cards' => false
            ));
            ?>
        <?php else : ?>
        <div class="no-coupons-message">
            <p><?php esc_html_e('لا توجد كوبونات متاحة حالياً', 'wp-coupon'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php
// Reset global query
wp_reset_postdata();
?>
